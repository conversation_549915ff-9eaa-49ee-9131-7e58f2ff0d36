import React from 'react';
import { useTranslation } from 'react-i18next';
import AuthRequired from '../components/AuthRequired';
import CreditsBurnAdmin from '../components/admin/CreditsBurnAdmin';

/**
 * CreditsBurnsAdminPage component
 * Main admin dashboard page
 */
const CreditsBurnsAdminPage = () => {
  const { t } = useTranslation(['admin', 'common']);

  return (
    <AuthRequired>
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
        <div className="container mx-auto px-4 py-8">
          {/* Page Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-white mb-4">
              {t('admin.title', { defaultValue: 'Manage Credits Burn Requests' })}
            </h1>
            <p className="text-gray-300 text-lg">
              {t('admin.subtitle', { defaultValue: 'Important: Write the hash value of the transaction in Note.' })}
            </p>
          </div>

          {/* Admin Sections */}
          <div className="space-y-8">
            {/* Credits Burn Admin Section */}
            <CreditsBurnAdmin />
            
            {/* Future admin sections can be added here */}
            {/* 
            <div className="bg-gradient-to-br from-blue-900/40 to-purple-900/20 backdrop-blur-md rounded-xl border border-white/10 p-6">
              <h2 className="text-xl font-semibold text-white mb-4">Other Admin Functions</h2>
              <p className="text-gray-400">Additional admin features will be added here...</p>
            </div>
            */}
          </div>
        </div>
      </div>
    </AuthRequired>
  );
};

export default CreditsBurnsAdminPage;
