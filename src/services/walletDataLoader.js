/**
 * 钱包数据加载器
 * 智能协调所有钱包相关数据的获取，避免重复 RPC 调用
 */

import rpcManager from './rpcManager';
import { solanaWalletService } from './solanaWalletService';

class WalletDataLoader {
  constructor() {
    // 加载状态跟踪
    this.loadingStates = new Map();
    // 数据缓存
    this.dataCache = new Map();
    // 加载队列
    this.loadQueue = [];
    // 是否正在处理队列
    this.processingQueue = false;
  }

  /**
   * 获取钱包的所有基础数据（SOL 余额 + CFX 余额）
   * @param {string} address - 钱包地址
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<{solBalance: Object, cfxBalance: Object}>}
   */
  async getWalletData(address, forceRefresh = false) {
    if (!address) {
      return {
        solBalance: { success: false, message: "No wallet address provided" },
        cfxBalance: { success: false, message: "No wallet address provided" }
      };
    }

    const cacheKey = `wallet_data_${address}`;
    
    // 检查是否正在加载
    if (this.loadingStates.has(cacheKey)) {
      return await this.loadingStates.get(cacheKey);
    }

    // 创建加载 Promise
    const loadPromise = this._loadWalletData(address, forceRefresh);
    this.loadingStates.set(cacheKey, loadPromise);

    try {
      const result = await loadPromise;
      return result;
    } finally {
      this.loadingStates.delete(cacheKey);
    }
  }

  /**
   * 实际执行钱包数据加载
   */
  async _loadWalletData(address, forceRefresh) {
    try {
      // 并发获取 SOL 和 CFX 余额，但限制并发数
      const [solBalanceResult, cfxBalanceResult] = await Promise.allSettled([
        rpcManager.getBalance(address, forceRefresh),
        // CFX 余额获取稍微延迟，避免同时发送太多请求
        new Promise(resolve => {
          setTimeout(async () => {
            try {
              const result = await rpcManager.getCfxTokenBalance(address, forceRefresh);
              resolve(result);
            } catch (error) {
              resolve({
                success: false,
                error,
                message: "Failed to get CFX balance"
              });
            }
          }, 100); // 100ms 延迟
        })
      ]);

      return {
        solBalance: solBalanceResult.status === 'fulfilled' 
          ? solBalanceResult.value 
          : { success: false, message: "Failed to get SOL balance" },
        cfxBalance: cfxBalanceResult.status === 'fulfilled' 
          ? cfxBalanceResult.value 
          : { success: false, message: "Failed to get CFX balance" }
      };
    } catch (error) {
      return {
        solBalance: { success: false, error, message: "Failed to get SOL balance" },
        cfxBalance: { success: false, error, message: "Failed to get CFX balance" }
      };
    }
  }

  /**
   * 批量获取多个钱包的数据
   * @param {string[]} addresses - 钱包地址数组
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Object>} 地址到数据的映射
   */
  async getBatchWalletData(addresses, forceRefresh = false) {
    if (!addresses || addresses.length === 0) {
      return {};
    }

    // 限制批量大小
    const batchSize = 3;
    const results = {};

    for (let i = 0; i < addresses.length; i += batchSize) {
      const batch = addresses.slice(i, i + batchSize);
      const batchPromises = batch.map(address => 
        this.getWalletData(address, forceRefresh)
      );

      const batchResults = await Promise.allSettled(batchPromises);
      
      batch.forEach((address, index) => {
        const result = batchResults[index];
        results[address] = result.status === 'fulfilled' 
          ? result.value 
          : {
              solBalance: { success: false, message: "Batch load failed" },
              cfxBalance: { success: false, message: "Batch load failed" }
            };
      });

      // 在批次之间添加延迟
      if (i + batchSize < addresses.length) {
        await new Promise(resolve => setTimeout(resolve, 300));
      }
    }

    return results;
  }

  /**
   * 预加载钱包数据（后台加载，不阻塞 UI）
   * @param {string} address - 钱包地址
   */
  async preloadWalletData(address) {
    if (!address) return;

    // 添加到预加载队列
    this.loadQueue.push({
      type: 'preload',
      address,
      timestamp: Date.now()
    });

    // 处理队列
    this._processQueue();
  }

  /**
   * 处理加载队列
   */
  async _processQueue() {
    if (this.processingQueue || this.loadQueue.length === 0) {
      return;
    }

    this.processingQueue = true;

    try {
      while (this.loadQueue.length > 0) {
        const item = this.loadQueue.shift();
        
        // 检查是否过期（超过 5 分钟的请求丢弃）
        if (Date.now() - item.timestamp > 5 * 60 * 1000) {
          continue;
        }

        try {
          await this.getWalletData(item.address, false);
          // 在预加载项目之间添加延迟
          await new Promise(resolve => setTimeout(resolve, 500));
        } catch (error) {
          // 预加载失败不影响其他操作
          if (import.meta.env.DEV) {
            console.warn('Preload failed for address:', item.address, error);
          }
        }
      }
    } finally {
      this.processingQueue = false;
    }
  }

  /**
   * 清除特定地址的缓存
   * @param {string} address - 钱包地址
   */
  clearAddressCache(address) {
    rpcManager.clearAddressCache(address);
    
    // 清除本地缓存
    const keysToDelete = [];
    for (const key of this.dataCache.keys()) {
      if (key.includes(address)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.dataCache.delete(key));
  }

  /**
   * 清除所有缓存
   */
  clearAllCache() {
    rpcManager.clearAllCache();
    this.dataCache.clear();
    this.loadingStates.clear();
    this.loadQueue.length = 0;
  }

  /**
   * 获取加载统计信息
   */
  getStats() {
    return {
      ...rpcManager.getCacheStats(),
      loadingStates: this.loadingStates.size,
      queueLength: this.loadQueue.length,
      processingQueue: this.processingQueue
    };
  }

  /**
   * 智能刷新策略
   * 根据用户行为和数据重要性决定刷新频率
   * @param {string} address - 钱包地址
   * @param {string} context - 刷新上下文（'connect', 'stake', 'manual', 'background'）
   */
  async smartRefresh(address, context = 'background') {
    if (!address) return;

    let forceRefresh = false;
    let delay = 0;

    switch (context) {
      case 'connect':
        // 钱包连接时，立即获取最新数据
        forceRefresh = true;
        delay = 0;
        break;
      case 'stake':
        // 质押操作后，强制刷新但稍微延迟
        forceRefresh = true;
        delay = 1000;
        break;
      case 'manual':
        // 用户手动刷新，立即强制刷新
        forceRefresh = true;
        delay = 0;
        break;
      case 'background':
        // 后台刷新，使用缓存优先
        forceRefresh = false;
        delay = 2000;
        break;
    }

    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    return await this.getWalletData(address, forceRefresh);
  }
}

// 创建单例实例
const walletDataLoader = new WalletDataLoader();

export default walletDataLoader;
