/**
 * 钱包连接管理器 - 统一管理所有钱包连接逻辑
 * 确保所有钱包操作都通过 solanaRpcService 进行
 */

import { solanaWalletService } from './solanaWalletService';
import solanaRpcService from './solanaRpcService';

class WalletConnectionManager {
  constructor() {
    this.isInitialized = false;
    this.connectionListeners = [];
    this.lastConnectionCheck = 0;
    this.connectionState = {
      isConnected: false,
      address: null,
      isRpcReady: false
    };
  }

  /**
   * 初始化钱包连接管理器
   */
  async initialize() {
    if (this.isInitialized) return true;

    try {
      // 初始化 RPC 服务（现在总是成功）
      await solanaRpcService.initialize();

      // 初始化钱包服务
      const walletInitialized = await solanaWalletService.initialize();
      if (!walletInitialized) {
        if (import.meta.env.DEV) {
          console.warn('⚠️ 钱包服务初始化失败，但不影响管理器启动');
        }
      }

      this.isInitialized = true;
      
      // 检查现有连接状态（不影响初始化成功）
      try {
        await this.refreshConnectionState();
      } catch (error) {
        if (import.meta.env.DEV) {
          console.warn('⚠️ 刷新连接状态失败，但不影响初始化:', error);
        }
      }
      
      if (import.meta.env.DEV) {
        console.log('✅ WalletConnectionManager 初始化完成');
      }
      
      return true;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('❌ WalletConnectionManager 初始化失败:', error);
      }
      return false;
    }
  }

  /**
   * 统一的钱包连接方法
   */
  async connect() {
    try {
      if (!this.isInitialized) {
        const initialized = await this.initialize();
        if (!initialized) {
          throw new Error('Failed to initialize wallet connection manager');
        }
      }

      // 使用钱包服务进行连接
      const result = await solanaWalletService.connect();
      
      if (result.success) {
        // 连接成功后刷新 RPC 连接
        await this.refreshRpcConnection();
        
        // 更新连接状态
        await this.refreshConnectionState();
        
        // 通知监听器
        this.notifyListeners();
        
        if (import.meta.env.DEV) {
          console.log('✅ 钱包连接成功，RPC 服务已优化');
        }
      }
      
      return result;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('❌ 钱包连接失败:', error);
      }
      return {
        success: false,
        error,
        message: error.message || 'Failed to connect wallet'
      };
    }
  }

  /**
   * 统一的钱包断开连接方法
   */
  async disconnect() {
    try {
      const result = await solanaWalletService.disconnect();
      
      if (result.success) {
        // 更新连接状态
        this.connectionState = {
          isConnected: false,
          address: null,
          isRpcReady: false
        };
        
        // 通知监听器
        this.notifyListeners();
        
        if (import.meta.env.DEV) {
          console.log('✅ 钱包已断开连接');
        }
      }
      
      return result;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('❌ 钱包断开连接失败:', error);
      }
      return {
        success: false,
        error,
        message: error.message || 'Failed to disconnect wallet'
      };
    }
  }

  /**
   * 刷新 RPC 连接
   */
  async refreshRpcConnection() {
    try {
      await solanaRpcService.refreshWalletConnection();
      
      // 验证 RPC 连接状态
      const status = solanaRpcService.getConnectionStatus();
      this.connectionState.isRpcReady = status.walletReady;
      
      if (import.meta.env.DEV) {
        console.log('🔗 RPC 连接已刷新:', {
          walletReady: status.walletReady
        });
      }
      
      return true;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.warn('⚠️ RPC 连接刷新失败:', error);
      }
      this.connectionState.isRpcReady = false;
      return false;
    }
  }

  /**
   * 刷新连接状态
   */
  async refreshConnectionState() {
    try {
      const isConnected = solanaWalletService.isWalletConnected();
      const address = solanaWalletService.getWalletAddress();
      const rpcStatus = solanaRpcService.getConnectionStatus();
      
      this.connectionState = {
        isConnected,
        address,
        isRpcReady: rpcStatus.walletReady
      };
      
      this.lastConnectionCheck = Date.now();
      
      return this.connectionState;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.warn('⚠️ 刷新连接状态失败:', error);
      }
      return this.connectionState;
    }
  }

  /**
   * 检查连接健康状态（轻量级）
   */
  async checkConnectionHealth() {
    const now = Date.now();
    
    // 避免频繁检查，最少间隔 5 秒
    if (now - this.lastConnectionCheck < 5000) {
      return this.connectionState;
    }
    
    try {
      const currentState = await this.refreshConnectionState();
      
      // 如果钱包连接但 RPC 未准备好，尝试刷新 RPC 连接
      if (currentState.isConnected && !currentState.isRpcReady) {
        await this.refreshRpcConnection();
        // 重新检查状态
        await this.refreshConnectionState();
      }
      
      return this.connectionState;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.warn('⚠️ 连接健康检查失败:', error);
      }
      return this.connectionState;
    }
  }

  /**
   * 获取当前连接状态
   */
  getConnectionState() {
    return { ...this.connectionState };
  }

  /**
   * 判断是否需要重新连接
   */
  needsReconnection() {
    const state = this.getConnectionState();
    return state.isConnected && !state.isRpcReady;
  }

  /**
   * 添加连接状态监听器
   */
  addConnectionListener(listener) {
    if (typeof listener === 'function') {
      this.connectionListeners.push(listener);
    }
  }

  /**
   * 移除连接状态监听器
   */
  removeConnectionListener(listener) {
    const index = this.connectionListeners.indexOf(listener);
    if (index > -1) {
      this.connectionListeners.splice(index, 1);
    }
  }

  /**
   * 通知所有监听器
   */
  notifyListeners() {
    const state = this.getConnectionState();
    this.connectionListeners.forEach(listener => {
      try {
        listener(state);
      } catch (error) {
        if (import.meta.env.DEV) {
          console.warn('连接状态监听器执行失败:', error);
        }
      }
    });
  }

  /**
   * 获取钱包余额（通过优化的 RPC）
   */
  async getBalance(forceRefresh = false) {
    if (!this.connectionState.isConnected) {
      return {
        success: false,
        message: 'Wallet not connected'
      };
    }

    return await solanaWalletService.getBalance(forceRefresh);
  }

  /**
   * 获取 CFX 代币余额（通过优化的 RPC）
   */
  async getCfxBalance(forceRefresh = false) {
    if (!this.connectionState.isConnected) {
      return {
        success: false,
        message: 'Wallet not connected'
      };
    }

    return await solanaWalletService.getCfxTokenBalance(forceRefresh);
  }
}

// 创建单例实例
const walletConnectionManager = new WalletConnectionManager();

export default walletConnectionManager; 