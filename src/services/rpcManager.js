/**
 * RPC 调用管理器
 * 通过请求去重、缓存和批量处理来优化 RPC 调用
 */

import { PublicKey } from '@solana/web3.js';
import solanaRpcService from './solanaRpcService';

class RPCManager {
  constructor() {
    // 请求缓存
    this.cache = new Map();
    // 进行中的请求
    this.pendingRequests = new Map();
    // 批量请求队列
    this.batchQueue = [];
    // 批量处理定时器
    this.batchTimer = null;
    // 缓存过期时间（毫秒）
    this.cacheExpiry = {
      balance: 30000,        // SOL 余额缓存 30 秒
      cfxBalance: 30000,     // CFX 余额缓存 30 秒
      accountInfo: 15000,    // 账户信息缓存 15 秒
      tokenAccounts: 30000,  // 代币账户缓存 30 秒
    };
  }

  /**
   * 生成缓存键
   */
  getCacheKey(method, params) {
    return `${method}:${JSON.stringify(params)}`;
  }

  /**
   * 检查缓存是否有效
   */
  isCacheValid(cacheKey, expiry) {
    const cached = this.cache.get(cacheKey);
    if (!cached) return false;
    
    return Date.now() - cached.timestamp < expiry;
  }

  /**
   * 获取缓存数据
   */
  getCachedData(cacheKey) {
    const cached = this.cache.get(cacheKey);
    return cached ? cached.data : null;
  }

  /**
   * 设置缓存数据
   */
  setCachedData(cacheKey, data) {
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * 清理过期缓存
   */
  cleanExpiredCache() {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      // 使用最长的缓存时间作为清理标准
      if (now - value.timestamp > Math.max(...Object.values(this.cacheExpiry))) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * 获取钱包 SOL 余额（带缓存和去重）
   */
  async getBalance(address, forceRefresh = false) {
    const cacheKey = this.getCacheKey('getBalance', [address]);
    
    // 检查缓存
    if (!forceRefresh && this.isCacheValid(cacheKey, this.cacheExpiry.balance)) {
      return this.getCachedData(cacheKey);
    }

    // 检查是否有相同的请求正在进行
    if (this.pendingRequests.has(cacheKey)) {
      return await this.pendingRequests.get(cacheKey);
    }

    // 创建新请求
    const requestPromise = this._executeBalanceRequest(address);
    this.pendingRequests.set(cacheKey, requestPromise);

    try {
      const result = await requestPromise;
      
      // 缓存成功的结果
      if (result.success) {
        this.setCachedData(cacheKey, result);
      }
      
      return result;
    } finally {
      this.pendingRequests.delete(cacheKey);
    }
  }

  /**
   * 执行余额请求
   */
  async _executeBalanceRequest(address) {
    try {
      return await solanaRpcService.getBalance(address);
    } catch (error) {
      return {
        success: false,
        error,
        message: error.message || "获取余额失败"
      };
    }
  }

  /**
   * 获取 CFX 代币余额（优化版）
   */
  async getCfxTokenBalance(address, forceRefresh = false) {
    const cacheKey = this.getCacheKey('getCfxTokenBalance', [address]);
    
    // 检查缓存
    if (!forceRefresh && this.isCacheValid(cacheKey, this.cacheExpiry.cfxBalance)) {
      return this.getCachedData(cacheKey);
    }

    // 检查是否有相同的请求正在进行
    if (this.pendingRequests.has(cacheKey)) {
      return await this.pendingRequests.get(cacheKey);
    }

    // 创建新请求
    const requestPromise = this._executeCfxBalanceRequest(address);
    this.pendingRequests.set(cacheKey, requestPromise);

    try {
      const result = await requestPromise;
      
      // 缓存成功的结果
      if (result.success) {
        this.setCachedData(cacheKey, result);
      }
      
      return result;
    } finally {
      this.pendingRequests.delete(cacheKey);
    }
  }

  /**
   * 执行 CFX 余额请求
   */
  async _executeCfxBalanceRequest(address) {
    try {
      return await solanaRpcService.getCfxTokenBalance(address);
    } catch (error) {
      return {
        success: false,
        error,
        message: error.message || "获取 CFX 余额失败"
      };
    }
  }

  /**
   * 获取账户信息（带缓存和去重）
   */
  async getAccountInfo(address, forceRefresh = false) {
    const cacheKey = this.getCacheKey('getAccountInfo', [address]);
    
    // 检查缓存
    if (!forceRefresh && this.isCacheValid(cacheKey, this.cacheExpiry.accountInfo)) {
      return this.getCachedData(cacheKey);
    }

    // 检查是否有相同的请求正在进行
    if (this.pendingRequests.has(cacheKey)) {
      return await this.pendingRequests.get(cacheKey);
    }

    // 创建新请求
    const requestPromise = this._executeAccountInfoRequest(address);
    this.pendingRequests.set(cacheKey, requestPromise);

    try {
      const result = await requestPromise;
      
      // 缓存成功的结果
      if (result.success) {
        this.setCachedData(cacheKey, result);
      }
      
      return result;
    } finally {
      this.pendingRequests.delete(cacheKey);
    }
  }

  /**
   * 执行账户信息请求
   */
  async _executeAccountInfoRequest(address) {
    try {
      return await solanaRpcService.getAccountInfo(address);
    } catch (error) {
      return {
        success: false,
        error,
        message: error.message || "获取账户信息失败"
      };
    }
  }

  /**
   * 批量获取多个地址的余额
   */
  async getBatchBalances(addresses, forceRefresh = false) {
    const results = {};
    const uncachedAddresses = [];

    // 检查缓存
    for (const address of addresses) {
      const cacheKey = this.getCacheKey('getBalance', [address]);
      if (!forceRefresh && this.isCacheValid(cacheKey, this.cacheExpiry.balance)) {
        results[address] = this.getCachedData(cacheKey);
      } else {
        uncachedAddresses.push(address);
      }
    }

    // 并发获取未缓存的余额（但限制并发数）
    if (uncachedAddresses.length > 0) {
      const batchSize = 3; // 限制并发数为 3
      for (let i = 0; i < uncachedAddresses.length; i += batchSize) {
        const batch = uncachedAddresses.slice(i, i + batchSize);
        const batchPromises = batch.map(address => this.getBalance(address, forceRefresh));
        
        const batchResults = await Promise.allSettled(batchPromises);
        
        batch.forEach((address, index) => {
          const result = batchResults[index];
          if (result.status === 'fulfilled') {
            results[address] = result.value;
          } else {
            results[address] = {
              success: false,
              error: result.reason,
              message: "批量获取余额失败"
            };
          }
        });

        // 在批次之间添加短暂延迟
        if (i + batchSize < uncachedAddresses.length) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }
    }

    return results;
  }

  /**
   * 清除特定地址的缓存
   */
  clearAddressCache(address) {
    const keysToDelete = [];
    for (const key of this.cache.keys()) {
      if (key.includes(address)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * 清除所有缓存
   */
  clearAllCache() {
    this.cache.clear();
    this.pendingRequests.clear();
  }

  /**
   * 获取代币库余额（带缓存）
   * @param {string} tokenVaultAddress - 代币库地址
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<number>} 代币库余额
   */
  async getTokenVaultBalance(tokenVaultAddress, forceRefresh = false) {
    const cacheKey = this.getCacheKey('getTokenVaultBalance', [tokenVaultAddress]);

    // 检查缓存
    if (!forceRefresh && this.isCacheValid(cacheKey, this.cacheExpiry.accountInfo)) {
      const cached = this.getCachedData(cacheKey);
      return cached !== null ? cached : 0;
    }

    // 检查是否有相同的请求正在进行
    if (this.pendingRequests.has(cacheKey)) {
      return await this.pendingRequests.get(cacheKey);
    }

    // 创建新请求
    const requestPromise = this._executeTokenVaultBalanceRequest(tokenVaultAddress);
    this.pendingRequests.set(cacheKey, requestPromise);

    try {
      const result = await requestPromise;

      // 缓存结果
      this.setCachedData(cacheKey, result);

      return result;
    } finally {
      this.pendingRequests.delete(cacheKey);
    }
  }

  /**
   * 执行代币库余额请求
   */
  async _executeTokenVaultBalanceRequest(tokenVaultAddress) {
    try {
      const connection = await solanaRpcService.getConnection();
      const { PublicKey } = await import('@solana/web3.js');

      const tokenVaultInfo = await connection.getParsedAccountInfo(
        new PublicKey(tokenVaultAddress)
      );

      if (tokenVaultInfo.value && tokenVaultInfo.value.data.parsed) {
        const tokenAmount = tokenVaultInfo.value.data.parsed.info.tokenAmount;
        return parseFloat(tokenAmount.uiAmount) || 0;
      } else {
        return 0;
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("获取代币库余额失败:", error);
      }
      return 0;
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return {
      cacheSize: this.cache.size,
      pendingRequests: this.pendingRequests.size,
      cacheKeys: Array.from(this.cache.keys())
    };
  }
}

// 创建单例实例
const rpcManager = new RPCManager();

// 定期清理过期缓存
setInterval(() => {
  rpcManager.cleanExpiredCache();
}, 60000); // 每分钟清理一次

export default rpcManager;
