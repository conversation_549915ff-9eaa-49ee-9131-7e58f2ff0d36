import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import { supabase } from '../../services/supabase';
import { notify } from '../ui/Notification';

/**
 * CreditsBurnAdmin component
 * Admin interface for managing credits burn requests
 */
const CreditsBurnAdmin = () => {
  const { t } = useTranslation(['admin', 'common']);
  const { user } = useAuth();

  const [burnRequests, setBurnRequests] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedTab, setSelectedTab] = useState('all'); // 'all', 'pending', 'completed'
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);
  const [isWhitelistUser, setIsWhitelistUser] = useState(false);
  const [showNoteTooltipId, setShowNoteTooltipId] = useState(null);
  const [copiedNoteId, setCopiedNoteId] = useState(null);
  const tooltipRef = useRef(null);

  // Check if user is whitelist user
  useEffect(() => {
    checkWhitelistStatus();
  }, [user]);

  // Load burn requests when tab or page changes
  useEffect(() => {
    if (isWhitelistUser) {
      loadBurnRequests();
    }
  }, [selectedTab, currentPage, isWhitelistUser]);

  // 点击外部关闭 tooltip
  useEffect(() => {
    if (showNoteTooltipId === null) return;
    function handleClickOutside(event) {
      if (tooltipRef.current && !tooltipRef.current.contains(event.target)) {
        setShowNoteTooltipId(null);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showNoteTooltipId]);

  const checkWhitelistStatus = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('whitelist_users')
        .select('*')
        .eq('user_id', user.id);

      if (error) {
        console.error('Error checking whitelist status:', error);
        return;
      }

      const isWhitelisted = data && data.length > 0;
      setIsWhitelistUser(isWhitelisted);

      if (!isWhitelisted) {
        notify.error('Access denied: Only whitelist users can access this page');
      }
    } catch (error) {
      console.error('Error checking whitelist status:', error);
    }
  };

  const loadBurnRequests = async () => {
    setLoading(true);
    try {
      const offset = (currentPage - 1) * pageSize;
      
      let rpcFunction, rpcParams;
      
      if (selectedTab === 'pending') {
        // Use get_pending_cfx_payments for pending requests
        rpcFunction = 'get_pending_cfx_payments';
        rpcParams = {
          p_limit: pageSize,
          p_offset: offset
        };
      } else {
        // Use get_all_burn_requests for all requests
        rpcFunction = 'get_all_burn_requests';
        rpcParams = {
          p_limit: pageSize,
          p_offset: offset
        };
      }

      const { data, error } = await supabase.rpc(rpcFunction, rpcParams);

      if (error) {
        console.error(`Error calling ${rpcFunction}:`, error);
        notify.error(`Failed to load burn requests: ${error.message}`);
        return;
      }

      console.log(`${rpcFunction} result:`, data);

      if (data?.success) {
        let requests = data.data?.requests || [];
        
        // Filter by status if needed (for 'all' tab, we might want to filter completed)
        if (selectedTab === 'completed') {
          requests = requests.filter(req => req.status === 'completed');
        }

        setBurnRequests(requests);
        setTotalCount(data.data?.total_count || 0);
      } else {
        console.error('Database function returned error:', data);
        setBurnRequests([]);
        setTotalCount(0);
      }
    } catch (error) {
      console.error('Error loading burn requests:', error);
      notify.error('Failed to load burn requests');
      setBurnRequests([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  };

  const updateRequestStatus = async (requestId, newStatus, adminNotes = '') => {
    try {
      const { error } = await supabase
        .from('credits_burn_requests')
        .update({
          status: newStatus,
          admin_notes: adminNotes,
          completed_at: newStatus === 'completed' ? new Date().toISOString() : null
        })
        .eq('id', requestId);

      if (error) {
        console.error('Error updating request status:', error);
        notify.error(`Failed to update request: ${error.message}`);
        return false;
      }

      notify.success(`Request ${newStatus === 'completed' ? 'completed' : 'updated'} successfully`);
      await loadBurnRequests(); // Reload data
      return true;
    } catch (error) {
      console.error('Error updating request status:', error);
      notify.error('Failed to update request');
      return false;
    }
  };

  const handleCompleteRequest = async (request) => {
    const confirmMessage = `Mark request as completed?\n\nUser: ${request.user_email}\nAmount: ${request.burn_amount} Credits → ${Number(request.expected_cfx_amount).toLocaleString()} CFX\nWallet: ${request.wallet_address}`;
    
    if (!window.confirm(confirmMessage)) {
      return;
    }

    const adminNotes = prompt('请填写交易哈希值（只允许填写交易哈希值）：') || '';
    await updateRequestStatus(request.id, 'completed', adminNotes);
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'pending_payment':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-300 border border-yellow-400/40">
            Pending Payment
          </span>
        );
      case 'completed':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-300 border border-green-400/40">
            Completed
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-500/20 text-gray-300 border border-gray-400/40">
            {status}
          </span>
        );
    }
  };

  const totalPages = Math.ceil(totalCount / pageSize);

  if (!user) {
    return (
      <div className="text-center text-gray-400 py-8">
        Please log in to access this page.
      </div>
    );
  }

  if (!isWhitelistUser) {
    return (
      <div className="text-center text-red-400 py-8">
        <div className="text-xl font-semibold mb-2">Access Denied</div>
        <div>Only whitelist users can access the Credits Burn Admin panel.</div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="bg-gradient-to-br from-blue-900/40 to-purple-900/20 backdrop-blur-md rounded-xl border border-white/10 p-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-white">Credits Burn Admin</h1>
          <div className="text-sm text-gray-400">
            Total Requests: {totalCount}
          </div>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mb-6 bg-black/20 rounded-lg p-1">
          {[
            { key: 'all', label: 'All Requests' },
            { key: 'pending', label: 'Pending Payment' },
            { key: 'completed', label: 'Completed' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => {
                setSelectedTab(tab.key);
                setCurrentPage(1);
              }}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedTab === tab.key
                  ? 'bg-blue-500/30 text-blue-300'
                  : 'text-gray-400 hover:text-white hover:bg-white/10'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Loading */}
        {loading && (
          <div className="text-center text-gray-400 py-8">
            <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
            Loading burn requests...
          </div>
        )}

        {/* Requests Table */}
        {!loading && (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-white/10">
                  <th className="text-left py-3 px-4 text-gray-300 font-medium">User</th>
                  <th className="text-left py-3 px-4 text-gray-300 font-medium">Amount</th>
                  <th className="text-left py-3 px-4 text-gray-300 font-medium">Wallet</th>
                  <th className="text-left py-3 px-4 text-gray-300 font-medium whitespace-nowrap">Status</th>
                  <th className="text-left py-3 px-4 text-gray-300 font-medium">Created</th>
                  <th className="text-left py-3 px-4 text-gray-300 font-medium whitespace-nowrap">Actions</th>
                </tr>
              </thead>
              <tbody>
                {burnRequests.length === 0 ? (
                  <tr>
                    <td colSpan="6" className="text-center py-8 text-gray-400">
                      No burn requests found
                    </td>
                  </tr>
                ) : (
                  burnRequests.map((request) => (
                    <tr key={request.id} className="border-b border-white/5 hover:bg-white/5">
                      <td className="py-3 px-4">
                        <div className="text-blue-300 font-medium">{request.user_email || 'Unknown'}</div>
                        <div className="text-xs text-gray-400">{request.user_id}</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-orange-300 font-medium">{request.burn_amount} Credits</div>
                        <div className="text-purple-300 text-xs">→ {Number(request.expected_cfx_amount).toLocaleString()} CFX</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-gray-300 font-mono text-xs break-all">
                          {request.wallet_address}
                        </div>
                      </td>
                      <td className="py-3 px-4 whitespace-nowrap" style={{ position: 'relative' }}>
                        {getStatusBadge(request.status)}
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-gray-300">{new Date(request.created_at).toLocaleDateString()}</div>
                        {request.completed_at && (
                          <div className="text-xs text-green-400">
                            Completed: {new Date(request.completed_at).toLocaleDateString()}
                          </div>
                        )}
                      </td>
                      <td className="py-3 px-4 whitespace-nowrap" style={{ position: 'relative' }}>
                        {request.status === 'pending_payment' && (
                          <button
                            onClick={() => handleCompleteRequest(request)}
                            className="px-3 py-1 bg-green-500/20 hover:bg-green-500/30 text-green-300 rounded text-xs font-medium transition-colors"
                          >
                            Mark Complete
                          </button>
                        )}
                        {request.admin_notes && (
                          <span
                            className="ml-2 cursor-pointer align-middle relative"
                            onClick={async () => {
                              setShowNoteTooltipId(showNoteTooltipId === request.id ? null : request.id);
                              try {
                                await navigator.clipboard.writeText(request.admin_notes);
                                setCopiedNoteId(request.id);
                                setTimeout(() => setCopiedNoteId(null), 1000);
                              } catch (e) {}
                            }}
                            tabIndex={0}
                          >
                            <svg className="inline w-4 h-4 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                              <line x1="12" y1="16" x2="12" y2="12" stroke="currentColor" strokeWidth="2"/>
                              <circle cx="12" cy="8" r="1" fill="currentColor"/>
                            </svg>
                            {showNoteTooltipId === request.id && (
                              <div
                                ref={tooltipRef}
                                className="absolute z-10 left-1/2 -translate-x-1/2 mt-2 min-w-[180px] max-w-xs bg-gray-900 text-white text-xs rounded shadow-lg p-3 border border-blue-400 whitespace-pre-line break-all"
                                style={{ top: '100%' }}
                              >
                                {request.admin_notes}
                              </div>
                            )}
                            {copiedNoteId === request.id && (
                              <div className="absolute left-1/2 -translate-x-1/2 mt-1 px-2 py-1 bg-green-600 text-white text-xs rounded shadow" style={{ top: '100%' }}>
                                已复制
                              </div>
                            )}
                          </span>
                        )}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-between items-center mt-6 pt-4 border-t border-white/10">
            <div className="text-sm text-gray-400">
              Page {currentPage} of {totalPages} ({totalCount} total)
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 bg-gray-500/20 hover:bg-gray-500/30 disabled:opacity-50 disabled:cursor-not-allowed text-gray-300 rounded text-sm transition-colors"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 bg-gray-500/20 hover:bg-gray-500/30 disabled:opacity-50 disabled:cursor-not-allowed text-gray-300 rounded text-sm transition-colors"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CreditsBurnAdmin;
