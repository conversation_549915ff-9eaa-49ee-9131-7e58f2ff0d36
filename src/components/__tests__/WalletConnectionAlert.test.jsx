import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi } from 'vitest';
import WalletConnectionAlert from '../WalletConnectionAlert';

// Mock the contexts
const mockUseAuth = vi.fn();
const mockUseWallet = vi.fn();

vi.mock('../../contexts/AuthContext', () => ({
  useAuth: () => mockUseAuth()
}));

vi.mock('../../contexts/WalletContext', () => ({
  useWallet: () => mockUseWallet()
}));

vi.mock('../../services/solanaRpcService', () => ({
  default: {
    getConnectionStatus: vi.fn(() => ({ walletReady: false })),
    checkWalletRealConnection: vi.fn(() => Promise.resolve(false))
  }
}));

describe('WalletConnectionAlert', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('不显示提示当用户未登录时', () => {
    // 模拟未登录状态
    mockUseAuth.mockReturnValue({
      user: null
    });
    
    mockUseWallet.mockReturnValue({
      isConnected: true,
      address: 'test-address',
      connectWallet: vi.fn()
    });

    render(<WalletConnectionAlert />);
    
    // 不应该显示任何提示
    expect(screen.queryByText('钱包连接异常')).not.toBeInTheDocument();
  });

  test('不显示提示当用户已登录但钱包未连接时', () => {
    // 模拟已登录但钱包未连接状态
    mockUseAuth.mockReturnValue({
      user: { id: 'test-user' }
    });
    
    mockUseWallet.mockReturnValue({
      isConnected: false,
      address: null,
      connectWallet: vi.fn()
    });

    render(<WalletConnectionAlert />);
    
    // 不应该显示任何提示
    expect(screen.queryByText('钱包连接异常')).not.toBeInTheDocument();
  });

  test('可能显示提示当用户已登录且钱包已连接时', () => {
    // 模拟已登录且钱包已连接状态
    mockUseAuth.mockReturnValue({
      user: { id: 'test-user' }
    });
    
    mockUseWallet.mockReturnValue({
      isConnected: true,
      address: 'test-address',
      connectWallet: vi.fn()
    });

    render(<WalletConnectionAlert />);
    
    // 在这种情况下，组件会进行检测
    // 但由于检测是异步的，我们只能确认组件被渲染了
    // 实际的提示显示取决于检测结果
  });

  test('组件正确处理缺失的 connectWallet 函数', () => {
    mockUseAuth.mockReturnValue({
      user: { id: 'test-user' }
    });
    
    mockUseWallet.mockReturnValue({
      isConnected: true,
      address: 'test-address',
      connectWallet: null // 模拟缺失的函数
    });

    // 应该不会抛出错误
    expect(() => {
      render(<WalletConnectionAlert />);
    }).not.toThrow();
  });
});
