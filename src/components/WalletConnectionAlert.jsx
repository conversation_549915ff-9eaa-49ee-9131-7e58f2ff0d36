import React, { useState, useEffect, useRef } from 'react';
import { useWallet } from '../contexts/WalletContext';
import { useAuth } from '../contexts/AuthContext';
import walletConnectionManager from '../services/walletConnectionManager';

/**
 * 钱包连接状态检测和提示组件
 * 检测钱包真实连接状态，如果发现严重不一致则提示用户重新连接
 * 优化后的版本：减少误报，改善用户体验
 */
const WalletConnectionAlert = () => {
  const { user } = useAuth();
  const { isConnected, address, connectWallet } = useWallet();
  const [showAlert, setShowAlert] = useState(false);
  const [checking, setChecking] = useState(false);
  const [reconnecting, setReconnecting] = useState(false);
  
  // 使用 ref 来跟踪连续检测失败的次数
  const consecutiveFailuresRef = useRef(0);
  const lastCheckTimeRef = useRef(0);
  
  // 检测参数配置
  const CONFIG = {
    MIN_CHECK_INTERVAL: 5000,        // 最小检测间隔：5秒
    FAILURE_THRESHOLD: 3,            // 连续失败阈值：3次
    CHECK_COOLDOWN: 30000,           // 检测冷却时间：30秒
    GRACE_PERIOD: 10000,             // 页面加载宽限期：10秒
  };

  // 页面加载时间
  const pageLoadTimeRef = useRef(Date.now());

  // 检测钱包连接状态（优化版）
  const checkWalletConnection = async () => {
    // 安全检查：确保用户已登录且钱包已连接
    if (!user || !isConnected || checking) {
      return;
    }

    // 检查最小检测间隔
    const now = Date.now();
    if (now - lastCheckTimeRef.current < CONFIG.MIN_CHECK_INTERVAL) {
      return;
    }

    // 页面加载宽限期，避免初始化时的误报
    if (now - pageLoadTimeRef.current < CONFIG.GRACE_PERIOD) {
      return;
    }

    setChecking(true);
    lastCheckTimeRef.current = now;

    try {
      // 1. 先进行轻量级检测
      const basicCheck = checkBasicWalletState();
      if (!basicCheck.isValid) {
        handleCheckFailure(basicCheck.reason);
        return;
      }

      // 2. 使用钱包连接管理器检查健康状态
      const connectionState = await walletConnectionManager.checkConnectionHealth();
      
      // 3. 判断是否需要显示警告
      const needsReconnection = walletConnectionManager.needsReconnection();
      const shouldShowAlert = needsReconnection && 
                             consecutiveFailuresRef.current >= CONFIG.FAILURE_THRESHOLD;

      if (shouldShowAlert && !showAlert) {
        if (import.meta.env.DEV) {
          console.warn('🔍 检测到钱包连接问题，显示用户提示', {
            connectionState,
            needsReconnection,
            consecutiveFailures: consecutiveFailuresRef.current
          });
        }
        setShowAlert(true);
      } else if (!shouldShowAlert && showAlert) {
        // 连接正常，隐藏警告
        setShowAlert(false);
        consecutiveFailuresRef.current = 0;
      }

      // 连接正常时重置失败计数
      if (!needsReconnection) {
        consecutiveFailuresRef.current = 0;
      }

    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('检测钱包连接状态时发生错误:', error);
      }
      handleCheckFailure('检测过程中发生错误');
    } finally {
      setChecking(false);
    }
  };

  // 基础钱包状态检查（轻量级）
  const checkBasicWalletState = () => {
    // 检查 window.solana 是否存在
    if (!window.solana) {
      return { isValid: false, reason: 'window.solana 不存在' };
    }

    // 检查基本连接状态
    if (!window.solana.isConnected) {
      return { isValid: false, reason: '钱包显示未连接' };
    }

    // 检查公钥
    if (!window.solana.publicKey) {
      return { isValid: false, reason: '钱包公钥不存在' };
    }

    return { isValid: true };
  };



  // 处理检测失败
  const handleCheckFailure = (reason) => {
    consecutiveFailuresRef.current += 1;
    
    if (import.meta.env.DEV) {
      console.warn(`钱包连接检测失败 (${consecutiveFailuresRef.current}/${CONFIG.FAILURE_THRESHOLD}):`, reason);
    }
  };

  // 优化后的检测触发逻辑
  useEffect(() => {
    if (!user || !isConnected || !address) {
      // 用户未登录或钱包未连接时隐藏警告并重置状态
      setShowAlert(false);
      consecutiveFailuresRef.current = 0;
      return;
    }

    // 延迟首次检测，给钱包服务更多初始化时间
    const initialTimer = setTimeout(() => {
      checkWalletConnection();
    }, CONFIG.GRACE_PERIOD);

    return () => clearTimeout(initialTimer);
  }, [user, isConnected, address]);

  // 定期检测钱包连接状态（降低频率）
  useEffect(() => {
    if (!user || !isConnected) {
      return;
    }

    // 使用更长的检测间隔，减少对用户体验的影响
    const interval = setInterval(() => {
      checkWalletConnection();
    }, CONFIG.CHECK_COOLDOWN);

    return () => clearInterval(interval);
  }, [user, isConnected]);

  // 处理重新连接（优化版）
  const handleReconnect = async () => {
    setReconnecting(true);
    try {
      // 首先尝试刷新 RPC 连接，这通常能解决大部分问题
      const rpcRefreshed = await walletConnectionManager.refreshRpcConnection();
      
      if (rpcRefreshed) {
        // RPC 连接成功，检查是否还需要重新连接
        const stillNeedsReconnection = walletConnectionManager.needsReconnection();
        
        if (!stillNeedsReconnection) {
          setShowAlert(false);
          consecutiveFailuresRef.current = 0;
          
          if (window.notify) {
            window.notify.success('钱包连接已优化！');
          }
          return;
        }
      }
      
      // 如果 RPC 刷新不够，进行完整的重新连接
      if (connectWallet) {
        const result = await connectWallet();
        if (result && result.success) {
          setShowAlert(false);
          consecutiveFailuresRef.current = 0;
          
          if (window.notify) {
            window.notify.success('钱包重新连接成功！');
          }
        } else {
          if (window.notify) {
            window.notify.error('钱包重新连接失败，请手动重试');
          }
        }
      } else {
        // 使用 walletConnectionManager 进行连接
        const result = await walletConnectionManager.connect();
        if (result && result.success) {
          setShowAlert(false);
          consecutiveFailuresRef.current = 0;
          
          if (window.notify) {
            window.notify.success('钱包连接已优化！');
          }
        } else {
          if (window.notify) {
            window.notify.error('钱包连接优化失败');
          }
        }
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('钱包连接优化失败:', error);
      }
      if (window.notify) {
        window.notify.error('钱包连接优化失败');
      }
    } finally {
      setReconnecting(false);
    }
  };

  // 关闭提示（添加冷却时间）
  const handleDismiss = () => {
    setShowAlert(false);
    consecutiveFailuresRef.current = 0; // 重置失败计数
    
    // 设置较长的冷却时间，避免频繁弹出
    lastCheckTimeRef.current = Date.now() + CONFIG.CHECK_COOLDOWN;
  };

  // 如果用户未登录或不需要显示提示，则不渲染组件
  if (!user || !showAlert) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 max-w-md">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-lg">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3 flex-1">
            <h3 className="text-sm font-medium text-yellow-800">
              钱包连接需要优化
            </h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>
                检测到钱包连接可能不稳定。重新连接钱包可以启用优化的 RPC 服务，提升质押功能的响应速度。
              </p>
            </div>
            <div className="mt-4 flex space-x-2">
              <button
                type="button"
                onClick={handleReconnect}
                disabled={reconnecting}
                className="bg-yellow-600 text-white px-3 py-1.5 rounded text-sm font-medium hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {reconnecting ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    重新连接中...
                  </span>
                ) : (
                  '优化连接'
                )}
              </button>
              <button
                type="button"
                onClick={handleDismiss}
                className="bg-white text-yellow-800 px-3 py-1.5 rounded text-sm font-medium border border-yellow-300 hover:bg-yellow-50 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"
              >
                稍后处理
              </button>
            </div>
          </div>
          <div className="ml-auto pl-3">
            <div className="-mx-1.5 -my-1.5">
              <button
                type="button"
                onClick={handleDismiss}
                className="inline-flex bg-yellow-50 rounded-md p-1.5 text-yellow-500 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-yellow-50 focus:ring-yellow-600"
              >
                <span className="sr-only">关闭</span>
                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WalletConnectionAlert;
