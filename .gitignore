# Dependencies
node_modules
.pnp
.pnp.js

# Production build
dist
dist-ssr
build

# Local development
*.local
docs

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Editor directories and files
*.sql
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Testing
coverage


# yarn
yarn.lock

# supabase environment file (contains secrets)
supabase/.env
supabase/.temp/
supabase/.branches/
supabase/.DS_Store

# tools directory
tools/
sql/