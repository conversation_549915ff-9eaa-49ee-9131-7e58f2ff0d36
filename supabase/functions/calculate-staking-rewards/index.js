// ===============================
// 计算质押积分奖励边缘函数
// ===============================

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// 导入模块化组件
import { getCurrentSlot } from './services/solana-rpc.js';
import { getStakePoolInfo, getUserStakeInfo, getUserStakeStatus, getUserStakeStatusMessage } from './services/stake-info.js';
import { getLastClaimInfo } from './services/database.js';
import { calculateRewards, getRewardsStatistics } from './services/rewards-calculator.js';
import { validateWalletAddress } from './utils/validation.js';
import { getCorsHeaders } from './utils/http.js';
import { checkMinClaimInterval, storeRewardClaim } from './services/claim-service.js';

// 创建 Supabase 客户端用于 JWT 验证
const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY');
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

// 用于 JWT 验证的客户端（使用匿名密钥）
const supabaseAuth = createClient(supabaseUrl, supabaseAnonKey);

// 用于数据库操作的客户端（使用服务密钥）
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

/**
 * 主处理函数 - 支持计算奖励和领取奖励两种操作
 */
async function handleRequest(req) {
  // 处理 OPTIONS 预检请求
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: getCorsHeaders()
    });
  }

  // 只允许 POST 请求
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Method not allowed'
      }),
      {
        status: 405,
        headers: getCorsHeaders()
      }
    );
  }

  try {
    // 解析请求体
    const requestBody = await req.json();
    const { action = 'calculate' } = requestBody;

    // 根据 action 参数决定处理方式
    if (action === 'claim') {
      return await handleClaimRequest(req, requestBody);
    } else {
      return await handleCalculateRequest(req, requestBody);
    }
  } catch (error) {
    console.error('Error in staking-rewards handler:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Internal server error',
        originalError: error.message
      }),
      {
        status: 500,
        headers: getCorsHeaders()
      }
    );
  }
}

/**
 * 处理计算奖励请求（原有逻辑）
 */
async function handleCalculateRequest(_req, requestBody) {
  try {
    // 调试：检查环境变量
    // const heliusKey = Deno.env.get('HELIUS_API_KEY');
    // console.log(`🔑 HELIUS_API_KEY: ${heliusKey ? heliusKey.substring(0, 8) + '...' + heliusKey.substring(heliusKey.length - 4) : '未设置'}`);
    // console.log(`🔗 SUPABASE_URL: ${Deno.env.get('SUPABASE_URL') || '未设置'}`);
    // console.log(`🔐 SERVICE_ROLE_KEY: ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ? '已设置' : '未设置'}`);

    // 从请求体中获取钱包地址
    const { wallet_address } = requestBody;

    // 验证输入
    if (!validateWalletAddress(wallet_address)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid wallet address format'
        }),
        {
          status: 400,
          headers: getCorsHeaders()
        }
      );
    }

    // 获取当前 slot
    const currentSlot = await getCurrentSlot();

    // 获取质押池信息
    const stakePoolResult = await getStakePoolInfo();
    if (!stakePoolResult.success) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Failed to fetch stake pool info',
          details: stakePoolResult.error
        }),
        {
          status: 500,
          headers: getCorsHeaders()
        }
      );
    }

    // 获取用户质押信息
    const userStakeResult = await getUserStakeInfo(wallet_address);

    // 获取完整的积分释放统计信息
    const rewardsStats = getRewardsStatistics(currentSlot);

    let userStakeInfo = null;
    let availableRewards = 0;
    let stakingDurationSeconds = 0;
    let isEligible = false;
    let userStakeStatus = 'not_staked';
    let statusMessage = '未质押';

    if (userStakeResult.success && userStakeResult.data) {
      userStakeInfo = userStakeResult.data;

      // 获取用户质押状态
      userStakeStatus = getUserStakeStatus(userStakeInfo, currentSlot);
      statusMessage = getUserStakeStatusMessage(userStakeStatus, userStakeInfo, currentSlot);

      // 计算质押时长（秒）
      const stakingDurationSlots = currentSlot - parseInt(userStakeInfo.lastStakeSlot);
      stakingDurationSeconds = Math.floor(stakingDurationSlots * 0.4); // 每个slot约400ms

      // 获取最后领取信息
      const lastClaimResult = await getLastClaimInfo(wallet_address);
      let lastClaimSlot = null;

      // 正确处理首次计算奖励的情况
      if (lastClaimResult.success && lastClaimResult.data && lastClaimResult.data.has_claimed_before) {
        // 用户之前领取过奖励，使用最后领取的 slot
        lastClaimSlot = lastClaimResult.data.last_claim_slot;
      } else {
        // 用户从未领取过奖励，lastClaimSlot 保持为 null
        // 这样 calculateRewards 函数会正确处理首次奖励计算
        lastClaimSlot = null;
      }

      // 计算可用奖励
      availableRewards = calculateRewards(
        userStakeInfo,
        stakePoolResult.data,
        currentSlot,
        lastClaimSlot
      );

      // 格式化显示数量（转换为更易读的格式）
      // CFX 使用 6 位小数精度 (1 CFX = 1,000,000 wei)
      const stakedCFX = Math.round(parseInt(userStakeInfo.stakedAmount) / 1000000); // 转换为 CFX
      // Credits 已经是整数形式，无需转换
      const rewardCredits = Math.round(availableRewards); // Credits 是整数

      // 调试：输出原始数值以确认转换正确性
      // console.log(`🔍 调试 - 原始质押数量: ${userStakeInfo.stakedAmount}, 转换后: ${stakedCFX} CFX`);

      console.log(`📊 地址：${wallet_address.slice(0, 8)}...${wallet_address.slice(-8)}，质押：${stakedCFX}CFX，奖励：${rewardCredits}Credits`);

      isEligible = !userStakeInfo.withdrawalRequested && parseInt(userStakeInfo.stakedAmount) > 0;
    } else if (!userStakeResult.success) {
      userStakeStatus = 'error';
      statusMessage = `错误: ${userStakeResult.error}`;
      console.log(`📊 地址：${wallet_address.slice(0, 8)}...${wallet_address.slice(-8)}，质押：ERROR，奖励：0Credits`);
    } else {
      // 当没有质押账户时，使用 getUserStakeStatus 函数处理 null 情况
      userStakeStatus = getUserStakeStatus(null, currentSlot);
      statusMessage = getUserStakeStatusMessage(userStakeStatus, null, currentSlot);
      console.log(`📊 地址：${wallet_address.slice(0, 8)}...${wallet_address.slice(-8)}，质押：0CFX，奖励：0Credits`);
    }

    // 返回包含详细积分释放信息的结果
    return new Response(
      JSON.stringify({
        success: true,
        data: {
          wallet_address,
          staked_amount: userStakeInfo ? userStakeInfo.stakedAmount : "0",
          staking_duration_seconds: stakingDurationSeconds,
          available_rewards: availableRewards,
          total_pool_staked: stakePoolResult.data.totalStaked,
          current_slot: currentSlot,
          last_claim_slot: userStakeInfo ? userStakeInfo.lastStakeSlot : null,
          is_eligible: isEligible,
          withdrawal_requested: userStakeInfo ? userStakeInfo.withdrawalRequested : false,

          // 用户质押状态信息
          stake_status: userStakeStatus,
          status_message: statusMessage,

          // 积分释放系统统计信息
          rewards_statistics: rewardsStats,

          // 调试信息
          debug_info: {
            stake_pool_data: stakePoolResult.data,
            user_stake_result: userStakeResult
          }
        }
      }),
      {
        status: 200,
        headers: getCorsHeaders()
      }
    );

  } catch (error) {
    console.error('Error in calculate-staking-rewards:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Internal server error',
        details: error.message
      }),
      {
        status: 500,
        headers: getCorsHeaders()
      }
    );
  }
}

/**
 * 处理领取奖励请求
 */
async function handleClaimRequest(req, requestBody) {
  try {
    // 获取并验证 JWT token
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'UNAUTHORIZED',
          message: 'Missing or invalid authorization header',
          details: {
            verification_step: 'jwt_validation',
            reason: 'missing_bearer_token'
          }
        }),
        {
          status: 401,
          headers: getCorsHeaders()
        }
      );
    }

    const token = authHeader.replace('Bearer ', '');

    // 验证 JWT token（使用匿名密钥客户端）
    const { data: { user }, error: jwtError } = await supabaseAuth.auth.getUser(token);

    if (jwtError || !user) {
      console.error('JWT verification failed:', jwtError);
      return new Response(
        JSON.stringify({
          success: false,
          error: 'UNAUTHORIZED',
          message: 'Invalid or expired token',
          details: {
            verification_step: 'jwt_validation',
            reason: 'token_verification_failed'
          }
        }),
        {
          status: 401,
          headers: getCorsHeaders()
        }
      );
    }

    console.log(`🔐 用户认证成功: ${user.email} (${user.id})`);

    const { wallet_address } = requestBody;

    // 验证钱包地址格式
    if (!validateWalletAddress(wallet_address)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'INVALID_WALLET_ADDRESS',
          message: 'Invalid wallet address format',
          details: {
            verification_step: 'wallet_format',
            reason: 'invalid_base58_format'
          }
        }),
        {
          status: 400,
          headers: getCorsHeaders()
        }
      );
    }

    // 1. 获取当前 slot
    const currentSlot = await getCurrentSlot();

    // 2. 获取质押池信息
    const stakePoolResult = await getStakePoolInfo();
    if (!stakePoolResult.success) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'STAKE_POOL_ERROR',
          message: 'Failed to fetch stake pool information',
          details: {
            verification_step: 'stake_pool_data',
            reason: stakePoolResult.error
          }
        }),
        {
          status: 500,
          headers: getCorsHeaders()
        }
      );
    }

    // 3. 获取用户质押信息
    const userStakeResult = await getUserStakeInfo(wallet_address);
    if (!userStakeResult.success) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'USER_STAKE_ERROR',
          message: 'Failed to fetch user stake information',
          details: {
            verification_step: 'user_stake_data',
            reason: userStakeResult.error
          }
        }),
        {
          status: 500,
          headers: getCorsHeaders()
        }
      );
    }

    // 4. 验证用户是否有质押
    if (!userStakeResult.data) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'NO_STAKING_ACCOUNT',
          message: 'No staking account found for this wallet',
          details: {
            verification_step: 'staking_status',
            reason: 'no_staking_account'
          }
        }),
        {
          status: 400,
          headers: getCorsHeaders()
        }
      );
    }

    const userStakeInfo = userStakeResult.data;

    // 5. 验证质押状态
    if (userStakeInfo.withdrawalRequested) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'WITHDRAWAL_REQUESTED',
          message: 'Cannot claim rewards after withdrawal request',
          details: {
            verification_step: 'staking_status',
            reason: 'withdrawal_already_requested'
          }
        }),
        {
          status: 400,
          headers: getCorsHeaders()
        }
      );
    }

    if (userStakeInfo.stakedAmount === '0') {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'NO_STAKED_AMOUNT',
          message: 'No CFX tokens are currently staked',
          details: {
            verification_step: 'staking_status',
            reason: 'zero_staked_amount'
          }
        }),
        {
          status: 400,
          headers: getCorsHeaders()
        }
      );
    }

    // 6. 获取最后领取信息
    const lastClaimResult = await getLastClaimInfo(wallet_address);
    let lastClaimSlot = null;
    let lastClaimTimestamp = null;

    if (lastClaimResult.success && lastClaimResult.data && lastClaimResult.data.has_claimed_before) {
      lastClaimSlot = lastClaimResult.data.last_claim_slot;
      lastClaimTimestamp = lastClaimResult.data.last_claim_timestamp;
    }

    // 7. 检查最小领取间隔（1分钟）
    if (!checkMinClaimInterval(lastClaimTimestamp, 60)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'CLAIM_TOO_FREQUENT',
          message: 'Must wait at least 60 seconds between claims',
          details: {
            verification_step: 'claim_interval',
            reason: 'minimum_interval_not_met',
            min_interval_seconds: 60
          }
        }),
        {
          status: 429,
          headers: getCorsHeaders()
        }
      );
    }

    // 8. 计算可用奖励
    const availableRewards = calculateRewards(
      userStakeInfo,
      stakePoolResult.data,
      currentSlot,
      lastClaimSlot
    );

    // 9. 验证奖励金额
    if (availableRewards <= 0) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'NO_REWARDS_AVAILABLE',
          message: 'No rewards available to claim at this time',
          details: {
            verification_step: 'reward_calculation',
            reason: 'zero_rewards_calculated',
            available_rewards: availableRewards
          }
        }),
        {
          status: 400,
          headers: getCorsHeaders()
        }
      );
    }

    // 9.5. 验证最小 claim 数量
    const MIN_CLAIM_AMOUNT = 0;
    if (availableRewards < MIN_CLAIM_AMOUNT) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'INSUFFICIENT_REWARDS',
          message: `Minimum claim amount is ${MIN_CLAIM_AMOUNT} credits. You have ${Math.round(availableRewards)} credits available.`,
          details: {
            verification_step: 'minimum_claim_amount',
            reason: 'below_minimum_threshold',
            available_rewards: Math.round(availableRewards),
            minimum_required: MIN_CLAIM_AMOUNT
          }
        }),
        {
          status: 400,
          headers: getCorsHeaders()
        }
      );
    }

    // 10. 计算质押时长
    const startSlot = lastClaimSlot ?
      Math.max(Number(userStakeInfo.lastStakeSlot), lastClaimSlot) :
      Number(userStakeInfo.lastStakeSlot);
    const stakingSlots = Math.max(0, currentSlot - startSlot);
    const stakingDurationSeconds = Math.floor(stakingSlots * 0.4); // 每个slot约400ms

    // 11. 存储奖励领取记录（使用管理员客户端）
    const storeResult = await storeRewardClaim(
      supabaseAdmin,
      user.id,
      wallet_address,
      availableRewards,
      userStakeInfo.stakedAmount,
      stakingDurationSeconds,
      stakePoolResult.data.totalStaked,
      currentSlot,
      lastClaimSlot
    );

    if (!storeResult.success) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'CLAIM_STORAGE_FAILED',
          message: 'Failed to store reward claim',
          details: {
            verification_step: 'database_storage',
            reason: storeResult.error
          }
        }),
        {
          status: 500,
          headers: getCorsHeaders()
        }
      );
    }

    // 12. 返回成功结果
    return new Response(
      JSON.stringify({
        success: true,
        data: {
          claimed_amount: availableRewards,
          new_balance: storeResult.data.new_balance,
          transaction_id: storeResult.data.transaction_id,
          claim_record_id: storeResult.data.claim_record_id,
          claim_timestamp: storeResult.data.claim_timestamp,
          verification_passed: true
        }
      }),
      {
        status: 200,
        headers: getCorsHeaders()
      }
    );

  } catch (error) {
    console.error('Error in claim-staking-rewards:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error occurred',
        details: {
          verification_step: 'internal_error',
          reason: error.message
        }
      }),
      {
        status: 500,
        headers: getCorsHeaders()
      }
    );
  }
}

// 启动服务
serve(handleRequest);
