// ===============================
// 配置常量和环境变量
// ===============================

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// 导入本地数据文件
import programIds from '../data/program-ids.json' with { type: 'json' };

// 积分奖励系统常量
export const REWARDS_CONFIG = {
  TOTAL_REWARDS_POOL: 1000000, // 100万积分
  YEAR_1_REWARDS: 500000,      // 第1年：50万积分
  YEAR_2_REWARDS: 250000,      // 第2年：25万积分
  YEAR_3_REWARDS: 250000,      // 第3年：25万积分
  SLOTS_PER_SECOND: 2.5,       // 每秒约2.5个slot (400ms per slot)
  SECONDS_PER_YEAR: 365 * 24 * 3600, // 一年的秒数

  // 积分释放起始时间配置 - 设置为当前时间之前的一个合理时间点
  REWARDS_START_SLOT: 349920000, // 积分释放起始 slot（对应 2025-06-29 02:00:00 UTC）
  REWARDS_START_TIMESTAMP: 1751162400, // 2025-06-29 02:00:00 UTC 的时间戳

  // 或者使用合约部署时间作为起始时间
  CONTRACT_DEPLOY_SLOT: null, // 从 program-ids.json 中获取
  CONTRACT_DEPLOY_TIMESTAMP: null, // 从 program-ids.json 中获取
};

// 计算每秒释放量
export const RELEASE_RATES = {
  YEAR_1: REWARDS_CONFIG.YEAR_1_REWARDS / REWARDS_CONFIG.SECONDS_PER_YEAR, // 0.0159 积分/秒
  YEAR_2: REWARDS_CONFIG.YEAR_2_REWARDS / REWARDS_CONFIG.SECONDS_PER_YEAR, // 0.0079 积分/秒
  YEAR_3: REWARDS_CONFIG.YEAR_3_REWARDS / REWARDS_CONFIG.SECONDS_PER_YEAR, // 0.0079 积分/秒
};

// 获取环境变量
export const HELIUS_API_KEY = Deno.env.get('HELIUS_API_KEY') || ''; // 从环境变量获取 API Key

// 创建 Supabase 客户端（使用匿名密钥用于 JWT 验证和正常数据库操作）
export const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? 'https://sujwqwyumtbmhwrbdrpe.supabase.co',
  Deno.env.get('SUPABASE_ANON_KEY')
);

// Solana RPC 配置
const buildHeliusRpcUrl = (network) => {
  const baseUrl = `https://${network}.helius-rpc.com`;
  return HELIUS_API_KEY ? `${baseUrl}/?api-key=${HELIUS_API_KEY}` : baseUrl;
};

export const HELIUS_RPC_URL = buildHeliusRpcUrl('mainnet');

// 合约地址（字符串格式）
export const PROGRAM_ID = programIds.programs.CFX_STAKE_CORE;
export const CFX_TOKEN_MINT = programIds.tokens.CFX_TOKEN_MINT;

// 导出 programIds 以供其他模块使用
export { programIds };
