// ===============================
// 加密工具函数
// ===============================

import { PROGRAM_ID } from '../config/constants.js';

/**
 * 辅助函数：字符串转 Uint8Array
 */
export function stringToUint8Array(str) {
  return new TextEncoder().encode(str);
}

/**
 * Base58 解码函数（简化版）
 */
export function base58Decode(s) {
  const alphabet = '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz';
  let decoded = 0n;
  let multi = 1n;

  for (let i = s.length - 1; i >= 0; i--) {
    const char = s[i];
    const index = alphabet.indexOf(char);
    if (index === -1) throw new Error('Invalid base58 character');
    decoded += BigInt(index) * multi;
    multi *= 58n;
  }

  // 转换为字节数组
  const bytes = [];
  while (decoded > 0n) {
    bytes.unshift(Number(decoded % 256n));
    decoded = decoded / 256n;
  }

  // 处理前导零
  for (let i = 0; i < s.length && s[i] === '1'; i++) {
    bytes.unshift(0);
  }

  return new Uint8Array(bytes);
}

/**
 * Base58 编码函数
 */
export function base58Encode(bytes) {
  const alphabet = '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz';
  let num = 0n;

  // 将字节转换为大整数
  for (let i = 0; i < bytes.length; i++) {
    num = num * 256n + BigInt(bytes[i]);
  }

  // 转换为 base58
  let encoded = '';
  while (num > 0n) {
    const remainder = num % 58n;
    encoded = alphabet[Number(remainder)] + encoded;
    num = num / 58n;
  }

  // 处理前导零
  for (let i = 0; i < bytes.length && bytes[i] === 0; i++) {
    encoded = '1' + encoded;
  }

  return encoded;
}

/**
 * 检查点是否在Ed25519曲线上
 * 基于Solana的实现
 */
export function isOnCurve(publicKeyBytes) {
  // Ed25519曲线检查
  // 如果最高位被设置，则该点在曲线上
  return (publicKeyBytes[31] & 0x80) !== 0;
}

/**
 * Solana标准的PDA查找函数
 * 完全按照Solana的实现方式
 */
export async function findProgramAddress(seeds, programId = PROGRAM_ID) {
  const programIdBytes = base58Decode(programId);
  const PDA_MARKER = stringToUint8Array('ProgramDerivedAddress');

  for (let bump = 255; bump >= 0; bump--) {
    try {
      // 计算总长度：所有种子 + bump + 程序ID + PDA标记
      let totalLength = 0;
      for (const seed of seeds) {
        totalLength += seed.length;
      }
      totalLength += 1; // bump
      totalLength += programIdBytes.length;
      totalLength += PDA_MARKER.length;

      // 合并所有数据：seeds + bump + programId + "ProgramDerivedAddress"
      const combined = new Uint8Array(totalLength);
      let offset = 0;

      // 添加所有种子
      for (const seed of seeds) {
        combined.set(seed, offset);
        offset += seed.length;
      }

      // 添加bump
      combined[offset] = bump;
      offset += 1;

      // 添加程序ID
      combined.set(programIdBytes, offset);
      offset += programIdBytes.length;

      // 添加PDA标记
      combined.set(PDA_MARKER, offset);

      // 计算SHA256哈希
      const hashBuffer = await crypto.subtle.digest('SHA-256', combined);
      const hash = new Uint8Array(hashBuffer);

      // 检查是否在Ed25519曲线上
      // 如果在曲线上，继续尝试下一个bump
      if (isOnCurve(hash)) {
        continue;
      }

      // 返回PDA地址（32字节）
      return base58Encode(hash.slice(0, 32));
    } catch (_error) {
      continue;
    }
  }

  throw new Error('Unable to find a valid program address');
}
