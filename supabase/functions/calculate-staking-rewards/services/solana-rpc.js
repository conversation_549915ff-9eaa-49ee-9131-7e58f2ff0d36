// ===============================
// Solana RPC 交互服务
// ===============================

import { HELIUS_RPC_URL } from '../config/constants.js';

/**
 * 调用 Solana RPC
 */
export async function callSolanaRPC(method, params = []) {
  try {
    const response = await fetch(HELIUS_RPC_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: Date.now(),
        method: method,
        params: params
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(`RPC Error: ${data.error.message}`);
    }

    return data.result;
  } catch (error) {
    console.error('RPC call failed:', error);
    throw error;
  }
}

/**
 * 获取当前 slot
 */
export async function getCurrentSlot() {
  return await callSolanaRPC('getSlot');
}

/**
 * 获取账户信息
 */
export async function getAccountInfo(address) {
  const result = await callSolanaRPC('getAccountInfo', [
    address,
    {
      encoding: 'base64',
      commitment: 'confirmed'
    }
  ]);

  if (!result || !result.value) {
    return null;
  }

  return {
    data: new Uint8Array(atob(result.value.data[0]).split('').map(c => c.charCodeAt(0))),
    owner: result.value.owner,
    lamports: result.value.lamports,
    executable: result.value.executable
  };
}
