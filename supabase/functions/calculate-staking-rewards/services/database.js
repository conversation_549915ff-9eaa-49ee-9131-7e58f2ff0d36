// ===============================
// 数据库操作服务
// ===============================

import { supabase } from '../config/constants.js';

/**
 * 获取用户最后领取信息
 */
export async function getLastClaimInfo(walletAddress) {
  try {
    const { data, error } = await supabase.rpc('get_last_claim_info', {
      p_wallet_address: walletAddress
    });

    if (error) {
      return {
        success: false,
        error: error.message
      };
    }

    return data;
  } catch (error) {
    return {
      success: false,
      error: 'Failed to get last claim info',
      originalError: error.message
    };
  }
}
