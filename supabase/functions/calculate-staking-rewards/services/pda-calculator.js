// ===============================
// PDA 计算服务
// ===============================

import { programIds } from '../config/constants.js';
import { base58Decode, stringToUint8Array, findProgramAddress } from '../utils/crypto.js';
import { PROGRAM_ID } from '../config/constants.js';

/**
 * 获取质押池 PDA - 直接使用已知地址
 * 从 program-ids.json 中获取已部署的质押池地址
 */
export function getStakePoolPDA() {
  // 直接使用已知的质押池地址，避免 PDA 计算问题
  const stakePoolAddress = programIds.deployed_accounts.stake_pool.address;
  console.log(`🏦 使用已知的质押池PDA: ${stakePoolAddress}`);
  return stakePoolAddress;
}

/**
 * 获取用户质押 PDA - 向后兼容版本
 * 尝试多种计算方式，确保所有地址都能正常工作
 */
export async function getUserStakePDA(userAddress) {
  try {
    // 获取质押池PDA
    const stakePoolPDA = getStakePoolPDA();

    // 方式1：当前的计算方式（保持向后兼容）
    const pda1 = await calculateUserStakePDA_Method1(userAddress, stakePoolPDA);

    // 方式2：标准 Solana 计算方式（与前端一致）
    const pda2 = await calculateUserStakePDA_Method2(userAddress, stakePoolPDA);

    // 返回计算结果，供后续验证使用
    return {
      method1: pda1,
      method2: pda2,
      // 默认使用方式1保持向后兼容
      primary: pda1
    };
  } catch (error) {
    console.error(`❌ 计算用户质押PDA失败:`, error);
    throw new Error(`Failed to calculate user stake PDA for ${userAddress}: ${error.message}`);
  }
}

/**
 * 方式1：当前的计算方式（向后兼容）
 */
async function calculateUserStakePDA_Method1(userAddress, stakePoolPDA) {
  try {
    // 将地址字符串转换为字节数组
    const userAddressBytes = base58Decode(userAddress);
    const stakePoolBytes = base58Decode(stakePoolPDA);

    // 计算PDA: ['user_stake', stakePoolPDA, userPublicKey]
    const seeds = [
      stringToUint8Array('user_stake'),
      stakePoolBytes,
      userAddressBytes
    ];

    const pda = await findProgramAddress(seeds, PROGRAM_ID);
    return pda;
  } catch (error) {
    console.error(`❌ 方式1计算失败:`, error);
    throw error;
  }
}

/**
 * 方式2：标准 Solana 计算方式（与前端保持一致）
 * 这里我们需要实现与 PublicKey.toBuffer() 完全一致的行为
 */
async function calculateUserStakePDA_Method2(userAddress, stakePoolPDA) {
  try {
    // TODO: 实现标准的 Solana PDA 计算方式
    // 这里暂时返回与方式1相同的结果，后续可以根据需要调整

    // 验证地址格式
    const userAddressBytes = base58Decode(userAddress);
    const stakePoolBytes = base58Decode(stakePoolPDA);

    if (userAddressBytes.length !== 32) {
      throw new Error(`Invalid user address length: ${userAddressBytes.length}, expected 32`);
    }
    if (stakePoolBytes.length !== 32) {
      throw new Error(`Invalid stake pool address length: ${stakePoolBytes.length}, expected 32`);
    }

    // 使用相同的计算方式，但可以在这里添加不同的实现
    const seeds = [
      stringToUint8Array('user_stake'),
      stakePoolBytes,
      userAddressBytes
    ];

    const pda = await findProgramAddress(seeds, PROGRAM_ID);
    return pda;
  } catch (error) {
    console.error(`❌ 方式2计算失败:`, error);
    throw error;
  }
}
