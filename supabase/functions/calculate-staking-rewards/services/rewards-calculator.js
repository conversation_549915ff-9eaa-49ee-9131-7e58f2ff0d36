// ===============================
// 奖励计算服务
// ===============================

import { REWARDS_CONFIG, RELEASE_RATES } from '../config/constants.js';

/**
 * 获取积分释放起始时间
 * 起始时间：2025-06-01 15:30:00 UTC
 * 这是积分奖励系统的"创世时间"，类似于区块链的创世区块
 */
export function getRewardsStartSlot() {
  // 优先使用环境变量配置的起始时间
  const envStartSlot = Deno.env.get('REWARDS_START_SLOT');
  if (envStartSlot) {
    return parseInt(envStartSlot);
  }

  // 使用配置中的起始时间：2025-06-01 15:30:00 UTC
  return REWARDS_CONFIG.REWARDS_START_SLOT;
}

/**
 * 获取积分释放起始时间的人类可读格式
 */
export function getRewardsStartTimeInfo() {
  const startSlot = getRewardsStartSlot();
  const startTimestamp = REWARDS_CONFIG.REWARDS_START_TIMESTAMP;
  const startDate = new Date(startTimestamp * 1000);

  return {
    slot: startSlot,
    timestamp: startTimestamp,
    date_utc: startDate.toISOString(),
    date_readable: startDate.toUTCString()
  };
}

/**
 * 计算从起始时间到指定时间的总释放积分
 * 增加了安全边界检查，确保不会超过总积分池
 */
export function calculateTotalReleasedRewards(currentSlot) {
  const startSlot = getRewardsStartSlot();

  // 如果当前时间还没到起始时间，返回0
  if (currentSlot <= startSlot) {
    return 0;
  }

  // 计算从起始时间到现在的总秒数
  const totalSlots = currentSlot - startSlot;
  const totalSeconds = totalSlots / REWARDS_CONFIG.SLOTS_PER_SECOND;

  let totalReleased = 0;
  let remainingSeconds = totalSeconds;

  // 第1年释放
  if (remainingSeconds > 0) {
    const year1Seconds = Math.min(remainingSeconds, REWARDS_CONFIG.SECONDS_PER_YEAR);
    totalReleased += year1Seconds * RELEASE_RATES.YEAR_1;
    remainingSeconds -= year1Seconds;
  }

  // 第2年释放
  if (remainingSeconds > 0) {
    const year2Seconds = Math.min(remainingSeconds, REWARDS_CONFIG.SECONDS_PER_YEAR);
    totalReleased += year2Seconds * RELEASE_RATES.YEAR_2;
    remainingSeconds -= year2Seconds;
  }

  // 第3年释放
  if (remainingSeconds > 0) {
    const year3Seconds = Math.min(remainingSeconds, REWARDS_CONFIG.SECONDS_PER_YEAR);
    totalReleased += year3Seconds * RELEASE_RATES.YEAR_3;
  }

  // 🔒 安全边界检查：确保不超过总积分池
  const calculatedAmount = Math.floor(totalReleased);
  const maxAllowed = REWARDS_CONFIG.TOTAL_REWARDS_POOL;

  if (calculatedAmount > maxAllowed) {
    console.warn(`⚠️ 计算的释放量 ${calculatedAmount} 超过了总积分池 ${maxAllowed}，已限制为最大值`);
    return maxAllowed;
  }

  return calculatedAmount;
}

/**
 * 计算下个周期（30天）的预估释放量
 */
export function calculateNext30DaysRelease(currentSlot) {
  const slotsIn30Days = 30 * 24 * 3600 * REWARDS_CONFIG.SLOTS_PER_SECOND; // 30天的slot数
  const futureSlot = currentSlot + slotsIn30Days;

  // 计算当前已释放量
  const currentReleased = calculateTotalReleasedRewards(currentSlot);

  // 计算30天后的总释放量
  const futureReleased = calculateTotalReleasedRewards(futureSlot);

  // 下个30天的释放量
  const next30DaysRelease = futureReleased - currentReleased;

  return {
    next_30_days_release: Math.floor(next30DaysRelease),
    daily_average: Math.floor(next30DaysRelease / 30),
    period_end_slot: futureSlot,
    period_end_date: new Date(Date.now() + 30 * 24 * 3600 * 1000).toISOString()
  };
}

/**
 * 获取当前所处的释放年份和阶段信息
 */
export function getCurrentReleasePhase(currentSlot) {
  const startSlot = getRewardsStartSlot();

  if (currentSlot <= startSlot) {
    return {
      phase: 'not_started',
      year: 0,
      progress_in_year: 0,
      current_year_rate: 0
    };
  }

  const elapsedSlots = currentSlot - startSlot;
  const elapsedSeconds = elapsedSlots / REWARDS_CONFIG.SLOTS_PER_SECOND;
  const elapsedYears = elapsedSeconds / REWARDS_CONFIG.SECONDS_PER_YEAR;

  let phase, year, currentYearRate, progressInYear;

  if (elapsedYears < 1) {
    phase = 'year_1';
    year = 1;
    currentYearRate = RELEASE_RATES.YEAR_1;
    progressInYear = elapsedYears;
  } else if (elapsedYears < 2) {
    phase = 'year_2';
    year = 2;
    currentYearRate = RELEASE_RATES.YEAR_2;
    progressInYear = elapsedYears - 1;
  } else if (elapsedYears < 3) {
    phase = 'year_3';
    year = 3;
    currentYearRate = RELEASE_RATES.YEAR_3;
    progressInYear = elapsedYears - 2;
  } else {
    phase = 'completed';
    year = 3;
    currentYearRate = 0;
    progressInYear = 1;
  }

  return {
    phase,
    year,
    progress_in_year: Math.min(progressInYear, 1),
    current_year_rate: currentYearRate,
    elapsed_years: elapsedYears,
    remaining_years: Math.max(0, 3 - elapsedYears)
  };
}

/**
 * 获取完整的积分释放统计信息
 */
export function getRewardsStatistics(currentSlot) {
  const startSlot = getRewardsStartSlot();
  const startTimeInfo = getRewardsStartTimeInfo();
  const totalReleased = calculateTotalReleasedRewards(currentSlot);
  const next30Days = calculateNext30DaysRelease(currentSlot);
  const currentPhase = getCurrentReleasePhase(currentSlot);
  const systemActive = currentSlot > startSlot;

  // 计算总体进度
  const totalProgress = Math.min(totalReleased / REWARDS_CONFIG.TOTAL_REWARDS_POOL, 1);
  const remainingRewards = Math.max(0, REWARDS_CONFIG.TOTAL_REWARDS_POOL - totalReleased);

  return {
    system_active: systemActive,
    start_time_info: startTimeInfo,
    current_slot: currentSlot,

    // 总体统计
    total_rewards_pool: REWARDS_CONFIG.TOTAL_REWARDS_POOL,
    total_released: Math.floor(totalReleased),
    remaining_rewards: Math.floor(remainingRewards),
    total_progress_percentage: (totalProgress * 100).toFixed(2),

    // 当前阶段信息
    current_phase: currentPhase,

    // 时间统计
    slots_since_start: Math.max(0, currentSlot - startSlot),
    days_since_start: Math.max(0, (currentSlot - startSlot) / REWARDS_CONFIG.SLOTS_PER_SECOND / 86400),

    // 未来30天预估
    next_30_days: next30Days,

    // 释放速率
    release_rates: {
      year_1_per_second: RELEASE_RATES.YEAR_1,
      year_2_per_second: RELEASE_RATES.YEAR_2,
      year_3_per_second: RELEASE_RATES.YEAR_3,
      year_1_per_day: RELEASE_RATES.YEAR_1 * 86400,
      year_2_per_day: RELEASE_RATES.YEAR_2 * 86400,
      year_3_per_day: RELEASE_RATES.YEAR_3 * 86400
    }
  };
}

/**
 * 计算用户质押奖励
 * 新逻辑：如果用户已申请提取，则从总奖励中扣除申请提取后的奖励
 */
export function calculateRewards(userStakeInfo, stakePoolInfo, currentSlot, lastClaimSlot) {
  const stakedAmount = parseFloat(userStakeInfo.stakedAmount);
  const totalPoolStaked = parseFloat(stakePoolInfo.totalStaked);
  const startSlot = getRewardsStartSlot();

  // 如果没有质押，返回0
  if (stakedAmount === 0) {
    return 0;
  }

  // 如果全网总质押为0，返回0（避免除零错误）
  if (totalPoolStaked === 0) {
    return 0;
  }

  // 1. 首先按照原有逻辑计算从质押开始到现在的总奖励
  const totalRewards = calculateRewardsForPeriod(
    userStakeInfo,
    stakePoolInfo,
    currentSlot,
    lastClaimSlot,
    startSlot
  );

  // 2. 如果用户已申请提取，需要扣除申请提取后的奖励
  if (userStakeInfo.withdrawalRequested) {
    // 计算申请提取的时间点
    const lockDurationSlots = parseFloat(stakePoolInfo.lockDurationSlots);
    const withdrawalRequestSlot = parseFloat(userStakeInfo.unlockSlot) - lockDurationSlots;

    // 计算从申请提取到现在的奖励（需要扣除的部分）
    const rewardsToDeduct = calculateRewardsForPeriod(
      userStakeInfo,
      stakePoolInfo,
      currentSlot,
      withdrawalRequestSlot, // 从申请提取时间开始计算
      withdrawalRequestSlot  // 起始时间就是申请提取时间
    );

    const finalRewards = Math.max(0, totalRewards - rewardsToDeduct);
    return finalRewards;
  }

  // 3. 如果用户未申请提取，返回总奖励
  return totalRewards;
}

/**
 * 计算指定时间段内的奖励（内部辅助函数）
 * 这是原有的计算逻辑，提取为独立函数
 */
function calculateRewardsForPeriod(userStakeInfo, stakePoolInfo, currentSlot, lastClaimSlot, startSlot) {
  const stakedAmount = parseFloat(userStakeInfo.stakedAmount);
  const totalPoolStaked = parseFloat(stakePoolInfo.totalStaked);
  const lastStakeSlot = parseFloat(userStakeInfo.lastStakeSlot);

  // 计算有效的奖励计算起始时间
  // 取以下时间的最大值：积分释放起始时间、用户最后质押时间、用户最后领取时间
  let rewardStartSlot = Math.max(startSlot, lastStakeSlot);
  if (lastClaimSlot) {
    rewardStartSlot = Math.max(rewardStartSlot, lastClaimSlot);
  }

  // 如果当前时间还没到有效起始时间，返回0
  if (currentSlot <= rewardStartSlot) {
    return 0;
  }

  // 计算有效质押时长（秒）
  const effectiveSlots = currentSlot - rewardStartSlot;
  const effectiveSeconds = effectiveSlots / REWARDS_CONFIG.SLOTS_PER_SECOND;

  // 如果有效时长为0，返回0
  if (effectiveSeconds <= 0) {
    return 0;
  }

  // 计算用户在总池中的占比
  const userStakeRatio = stakedAmount / totalPoolStaked;

  // 计算这段时间内应该释放的总积分
  const periodStartSlot = rewardStartSlot;
  const periodEndSlot = currentSlot;

  // 计算这个时间段内的总释放量
  const totalReleasedAtEnd = calculateTotalReleasedRewards(periodEndSlot);
  const totalReleasedAtStart = calculateTotalReleasedRewards(periodStartSlot);
  const periodTotalReleased = totalReleasedAtEnd - totalReleasedAtStart;

  // 用户在这个时间段内应得的奖励
  const userRewards = periodTotalReleased * userStakeRatio;

  // 返回整数积分（向下取整）
  return Math.floor(userRewards);
}
