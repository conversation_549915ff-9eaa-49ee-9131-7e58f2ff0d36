// ===============================
// 账户数据解析服务
// ===============================

import { base58Encode } from '../utils/crypto.js';

/**
 * 解析质押池数据
 * 基于 view-status.js 中的精确解析方法
 */
export function parseStakePoolData(data) {
  if (!data || data.length < 8) {
    throw new Error('Invalid stake pool data');
  }

  let offset = 8; // 跳过8字节判别器

  // 解析字段（按照 IDL 顺序）
  const authorityBytes = data.slice(offset, offset + 32);
  const authority = base58Encode(authorityBytes);
  offset += 32;

  const tokenMintBytes = data.slice(offset, offset + 32);
  const tokenMint = base58Encode(tokenMintBytes);
  offset += 32;

  const tokenVaultBytes = data.slice(offset, offset + 32);
  const tokenVault = base58Encode(tokenVaultBytes);
  offset += 32;

  // lockDurationSlots 在 totalStaked 之前！使用 DataView 精确解析
  const lockDurationView = new DataView(data.buffer, data.byteOffset + offset, 8);
  const lockDurationSlots = lockDurationView.getBigUint64(0, true); // little-endian
  offset += 8;

  // totalStaked 在 lockDurationSlots 之后，使用 DataView 精确解析
  const totalStakedView = new DataView(data.buffer, data.byteOffset + offset, 8);
  const totalStaked = totalStakedView.getBigUint64(0, true); // little-endian
  offset += 8;

  const emergencyMode = data[offset] === 1;
  offset += 1;

  const reentrancyGuard = data[offset] === 1;
  offset += 1;

  const bump = data[offset];

  return {
    authority: authority,
    tokenMint: tokenMint,
    tokenVault: tokenVault,
    lockDurationSlots: lockDurationSlots.toString(),
    totalStaked: totalStaked.toString(),
    emergencyMode,
    reentrancyGuard,
    bump
  };
}

/**
 * 解析用户质押数据
 * 基于 view-status.js 中的精确解析方法
 */
export function parseUserStakeData(data) {
  if (!data || data.length < 90) { // 8 + 32 + 32 + 8 + 8 + 8 + 1 + 1 = 98 bytes minimum
    throw new Error('Invalid user stake data - insufficient length');
  }

  let offset = 8; // 跳过8字节判别器

  // Parse fields according to UserStake struct
  const ownerBytes = data.slice(offset, offset + 32);
  const owner = base58Encode(ownerBytes);
  offset += 32;

  // Parse stakePool field
  const stakePoolBytes = data.slice(offset, offset + 32);
  const stakePool = base58Encode(stakePoolBytes);
  offset += 32;

  // Parse stakedAmount using manual little-endian calculation (exactly like frontend)
  const stakedAmountBytes = data.slice(offset, offset + 8);

  // 手动计算 little-endian 值以确保准确性（与前端完全一致）
  let stakedAmount = 0n;
  for (let i = 0; i < 8; i++) {
    stakedAmount += BigInt(stakedAmountBytes[i]) << BigInt(i * 8);
  }
  offset += 8;

  // Parse lastStakeSlot using DataView
  const lastStakeSlotView = new DataView(data.buffer, data.byteOffset + offset, 8);
  const lastStakeSlot = lastStakeSlotView.getBigUint64(0, true);
  offset += 8;

  // Parse unlockSlot using DataView
  const unlockSlotView = new DataView(data.buffer, data.byteOffset + offset, 8);
  const unlockSlot = unlockSlotView.getBigUint64(0, true);
  offset += 8;

  const withdrawalRequested = data[offset] === 1;

  return {
    owner: owner,
    stakePool: stakePool,
    stakedAmount: stakedAmount.toString(),
    lastStakeSlot: lastStakeSlot.toString(),
    unlockSlot: unlockSlot.toString(),
    withdrawalRequested
  };
}
