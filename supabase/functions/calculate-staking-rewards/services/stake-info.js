// ===============================
// 质押信息获取服务
// ===============================

import { PROGRAM_ID } from '../config/constants.js';
import { getAccountInfo } from './solana-rpc.js';
import { parseStakePoolData, parseUserStakeData } from './account-parser.js';
import { getStakePoolPDA, getUserStakePDA } from './pda-calculator.js';

// 用户质押状态枚举
export const UserStakeStatus = {
  NOT_STAKED: 'not_staked',           // 未质押
  STAKED: 'staked',                   // 已质押，未申请提取
  WITHDRAWAL_REQUESTED: 'withdrawal_requested', // 已申请提取，锁定期中
  READY_TO_WITHDRAW: 'ready_to_withdraw'       // 锁定期结束，可提取
};

/**
 * 获取质押池信息
 */
export async function getStakePoolInfo() {
  try {
    const stakePoolPDA = getStakePoolPDA();
    const accountInfo = await getAccountInfo(stakePoolPDA);

    if (!accountInfo) {
      return {
        success: false,
        error: 'Stake pool account not found'
      };
    }

    if (accountInfo.owner !== PROGRAM_ID) {
      return {
        success: false,
        error: 'Invalid stake pool account owner'
      };
    }

    const stakePoolData = parseStakePoolData(accountInfo.data);
    return {
      success: true,
      data: stakePoolData
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to fetch stake pool information',
      originalError: error.message
    };
  }
}

/**
 * 获取用户质押信息 - 向后兼容版本
 * 尝试多种 PDA 计算方式，确保能找到正确的账户
 */
export async function getUserStakeInfo(walletAddress) {
  try {
    // 首先尝试查找所有相关的程序账户
    const actualAccountAddress = await findAllProgramAccounts(walletAddress);

    // 获取多种 PDA 计算结果
    const pdaResults = await getUserStakePDA(walletAddress);

    let accountInfo = null;
    let usedPDA = null;
    let usedMethod = null;

    // 如果找到了实际的账户地址，优先使用它
    if (actualAccountAddress) {
      accountInfo = await getAccountInfo(actualAccountAddress);
      usedPDA = actualAccountAddress;
      usedMethod = 'actual_found';
    }

    // 如果实际账户没找到或不存在，尝试方式1（当前方式）
    if (!accountInfo) {
      accountInfo = await getAccountInfo(pdaResults.method1);
      usedPDA = pdaResults.method1;
      usedMethod = 'method1';
    }

    // 如果方式1没找到，尝试方式2
    if (!accountInfo) {
      accountInfo = await getAccountInfo(pdaResults.method2);
      usedPDA = pdaResults.method2;
      usedMethod = 'method2';
    }

    // 如果两种方式都没找到账户
    if (!accountInfo) {
      return {
        success: true,
        data: null, // 账户不存在
        debug: {
          userStakePDA_method1: pdaResults.method1,
          userStakePDA_method2: pdaResults.method2,
          accountExists: false,
          triedMethods: ['actual_found', 'method1', 'method2']
        }
      };
    }

    if (accountInfo.owner !== PROGRAM_ID) {
      return {
        success: false,
        error: 'Invalid user stake account owner',
        debug: {
          userStakePDA: usedPDA,
          usedMethod,
          actualOwner: accountInfo.owner,
          expectedOwner: PROGRAM_ID
        }
      };
    }

    const userStakeData = parseUserStakeData(accountInfo.data);

    // 验证所有者匹配
    if (userStakeData.owner !== walletAddress) {
      return {
        success: false,
        error: 'User stake account owner mismatch',
        debug: {
          userStakePDA: usedPDA,
          usedMethod,
          parsedOwner: userStakeData.owner,
          expectedOwner: walletAddress
        }
      };
    }

    return {
      success: true,
      data: userStakeData,
      debug: {
        userStakePDA: usedPDA,
        usedMethod,
        accountExists: true,
        allPDAs: pdaResults
      }
    };
  } catch (error) {
    console.error(`❌ 获取用户质押信息失败:`, error);
    return {
      success: false,
      error: 'Failed to fetch user stake information',
      originalError: error.message,
      debug: {
        errorStack: error.stack
      }
    };
  }
}

/**
 * 获取用户质押状态
 * 参考 src/services/stakingService/view-status.js 中的 getUserStakeStatus 函数
 */
export function getUserStakeStatus(userStake, currentSlot) {
  if (!userStake || userStake.stakedAmount === '0') {
    return UserStakeStatus.NOT_STAKED;
  }

  if (!userStake.withdrawalRequested) {
    return UserStakeStatus.STAKED;
  }

  if (currentSlot >= parseInt(userStake.unlockSlot)) {
    return UserStakeStatus.READY_TO_WITHDRAW;
  }

  return UserStakeStatus.WITHDRAWAL_REQUESTED;
}

/**
 * 获取用户质押状态的中文描述
 */
export function getUserStakeStatusMessage(status, userStake, currentSlot) {
  switch (status) {
    case UserStakeStatus.NOT_STAKED:
      return '未质押';
    case UserStakeStatus.STAKED:
      return '已质押，正在获得奖励';
    case UserStakeStatus.WITHDRAWAL_REQUESTED: {
      if (!userStake || !userStake.unlockSlot) {
        return '已申请提取，等待处理中';
      }
      const remainingSlots = parseInt(userStake.unlockSlot) - currentSlot;
      const remainingDays = Math.ceil(remainingSlots * 0.4 / 86400); // 每个slot约400ms
      return `已申请提取，还需等待约 ${remainingDays} 天`;
    }
    case UserStakeStatus.READY_TO_WITHDRAW:
      return '锁定期已结束，可以提取';
    default:
      return '状态未知';
  }
}

/**
 * 查找用户钱包的所有程序账户
 * 用于调试和发现实际的质押账户地址
 */
async function findAllProgramAccounts(walletAddress) {
  try {
    // 使用 getProgramAccounts 查找所有属于我们程序的账户
    const rpcUrl = `https://devnet.helius-rpc.com/?api-key=f2a4faf0-9f47-4a30-8a61-b75ef933edde`;

    const response = await fetch(rpcUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'getProgramAccounts',
        params: [
          PROGRAM_ID,
          {
            encoding: 'base64',
            filters: [
              {
                memcmp: {
                  offset: 8, // 跳过判别器，从 owner 字段开始
                  bytes: walletAddress
                }
              }
            ]
          }
        ]
      })
    });

    // 安全地解析 JSON 响应
    const responseText = await response.text();
    if (!responseText || responseText.trim().length === 0) {
      console.error('Empty response from getProgramAccounts RPC');
      return null;
    }
    
    let result;
    try {
      result = JSON.parse(responseText);
    } catch (parseError) {
      console.error('JSON Parse Error in getProgramAccounts:', parseError.message);
      return null;
    }

    if (result.result && result.result.length > 0) {
      // 返回第一个找到的账户地址（很可能就是质押账户）
      return result.result[0].pubkey;
    } else {
      return null;
    }
  } catch (error) {
    console.error(`❌ 查找程序账户失败:`, error);
    return null;
  }
}
