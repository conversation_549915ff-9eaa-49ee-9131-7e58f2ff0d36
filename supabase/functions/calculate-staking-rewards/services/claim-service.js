// ===============================
// 奖励领取服务
// ===============================

import { supabase } from '../config/constants.js';

/**
 * 验证 JWT Token 并获取用户信息
 */
export async function verifyJWTAndGetUser(authHeader) {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return {
      success: false,
      error: 'MISSING_AUTH_TOKEN',
      message: 'Authorization header is required'
    };
  }

  const token = authHeader.substring(7); // 移除 "Bearer " 前缀

  try {
    // 使用 Supabase 验证 JWT token
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      return {
        success: false,
        error: 'INVALID_JWT_TOKEN',
        message: 'Invalid or expired token'
      };
    }

    return {
      success: true,
      user
    };
  } catch (error) {
    return {
      success: false,
      error: 'JWT_VERIFICATION_FAILED',
      message: 'Failed to verify token',
      originalError: error.message
    };
  }
}

/**
 * 验证钱包所有权
 */
export async function verifyWalletOwnership(userId, walletAddress) {
  try {
    const { data, error } = await supabase
      .from('wallet_signatures')
      .select('signature, signed_message, is_active, expires_at, user_id')
      .eq('user_id', userId)
      .eq('wallet_address', walletAddress)
      .eq('is_active', true)
      .maybeSingle();

    if (error) {
      return {
        success: false,
        error: 'DATABASE_ERROR',
        message: 'Failed to verify wallet ownership',
        originalError: error.message
      };
    }

    if (!data) {
      return {
        success: false,
        error: 'WALLET_NOT_VERIFIED',
        message: 'Wallet address not verified for this user'
      };
    }

    // 检查签名是否过期
    if (data.expires_at && new Date() > new Date(data.expires_at)) {
      return {
        success: false,
        error: 'WALLET_SIGNATURE_EXPIRED',
        message: 'Wallet signature has expired'
      };
    }

    // 防止跨用户攻击
    if (data.user_id !== userId) {
      return {
        success: false,
        error: 'WALLET_USER_MISMATCH',
        message: 'Wallet address belongs to different user'
      };
    }

    return {
      success: true,
      walletSignature: data
    };
  } catch (error) {
    return {
      success: false,
      error: 'WALLET_VERIFICATION_FAILED',
      message: 'Failed to verify wallet ownership',
      originalError: error.message
    };
  }
}

/**
 * 检查最小领取间隔
 */
export function checkMinClaimInterval(lastClaimTimestamp, minIntervalSeconds = 60) {
  if (!lastClaimTimestamp) {
    return true; // 第一次领取
  }

  const now = new Date();
  const lastClaim = new Date(lastClaimTimestamp);
  const timeDiffSeconds = (now - lastClaim) / 1000;

  return timeDiffSeconds >= minIntervalSeconds;
}

/**
 * 存储奖励领取记录
 */
export async function storeRewardClaim(supabaseClient, userId, walletAddress, claimedAmount, stakedAmount, stakingDurationSeconds, totalPoolStaked, currentSlot, lastClaimSlot) {
  try {
    console.log('🔍 调用 store_staking_reward_claim 函数，参数:', {
      p_user_id: userId,
      p_wallet_address: walletAddress,
      p_claimed_amount: claimedAmount,
      p_staked_amount: stakedAmount,
      p_staking_duration_seconds: stakingDurationSeconds,
      p_total_pool_staked: totalPoolStaked,
      p_claim_slot: currentSlot,
      p_last_claim_slot: lastClaimSlot
    });

    const { data, error } = await supabaseClient.rpc('store_staking_reward_claim', {
      p_user_id: userId,
      p_wallet_address: walletAddress,
      p_claimed_amount: claimedAmount,
      p_staked_amount: stakedAmount,
      p_staking_duration_seconds: stakingDurationSeconds,
      p_total_pool_staked: totalPoolStaked,
      p_claim_slot: currentSlot,
      p_last_claim_slot: lastClaimSlot
    });

    console.log('📊 数据库函数返回结果:', { data, error });

    if (error) {
      console.error('❌ 数据库函数执行错误:', error);
      return {
        success: false,
        error: error.message || error.toString()
      };
    }

    // 检查函数返回的数据结构
    if (data && typeof data === 'object' && data.success === false) {
      console.error('❌ 函数返回失败结果:', data);
      return {
        success: false,
        error: data.message || 'Database function returned failure'
      };
    }

    console.log('✅ 存储奖励领取记录成功');
    return data;
  } catch (error) {
    console.error('❌ 存储奖励领取记录异常:', error);
    return {
      success: false,
      error: 'Failed to store reward claim',
      originalError: error.message
    };
  }
}
