<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- CSP is primarily set via HTTP headers in _headers file, but can also be set via meta tag -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: https:; connect-src 'self' wss://devnet.helius-rpc.com wss://mainnet.helius-rpc.com https://devnet.helius-rpc.com https://mainnet.helius-rpc.com http://127.0.0.1:8899 http://localhost:8899 https://api.deepseek.com https://*.supabase.co https://*.solana.com https://*.solflare.com https://*.phantom.app https://*.helius-rpc.com; frame-src 'none'; font-src 'self'; object-src 'none'; base-uri 'self'; form-action 'self'; worker-src 'self' blob:; child-src 'self' blob:;" />
    <!-- Security headers are set via HTTP headers in _headers file, not meta tags -->
    <link rel="icon" type="image/png" href="/logo.png" />
    <meta name="description" content="ChainFox - Blockchain Guard. Providing automated security analysis for blockchain projects and smart contracts." />
    <title>CHAINFOX - Blockchain Guard</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>