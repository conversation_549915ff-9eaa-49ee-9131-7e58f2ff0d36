/**
 * 混合 RPC 测试脚本
 * 测试钱包 RPC 和 Helius RPC 的智能选择功能
 */

import solanaRpcService from '../src/services/solanaRpcService.js';

// 测试钱包地址
const TEST_WALLET_ADDRESSES = [
  'HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH', // 示例地址1
  '9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM', // 示例地址2
  'DhzDoryP2a4rMK2bcWwJxrE2uW6ir81ES8ZwJJLcSKrt'  // 示例地址3
];

/**
 * 测试 RPC 连接状态
 */
async function testConnectionStatus() {
  console.log('\n🔗 === 测试 RPC 连接状态 ===');
  
  // 初始化服务
  console.log('初始化 RPC 服务...');
  const initSuccess = await solanaRpcService.initialize();
  console.log(`初始化结果: ${initSuccess ? '成功' : '失败'}`);
  
  // 获取连接状态
  const status = solanaRpcService.getConnectionStatus();
  console.log('\n连接状态:');
  console.log(`  Helius RPC: ${status.heliusReady ? '✅ 就绪' : '❌ 未就绪'}`);
  console.log(`  钱包 RPC: ${status.walletReady ? '✅ 就绪' : '❌ 未就绪'}`);
  console.log(`  Helius 端点: ${status.heliusEndpoint || '未知'}`);
  console.log(`  钱包端点: ${status.walletEndpoint || '未知'}`);
  
  return status;
}

/**
 * 测试余额获取（对比不同 RPC）
 */
async function testBalanceComparison() {
  console.log('\n💰 === 测试余额获取对比 ===');
  
  const testAddress = TEST_WALLET_ADDRESSES[0];
  console.log(`测试地址: ${testAddress}`);
  
  try {
    // 测试智能选择
    console.log('\n1️⃣ 智能选择 RPC:');
    const start1 = Date.now();
    const smartResult = await solanaRpcService.getBalance(testAddress);
    const time1 = Date.now() - start1;
    
    console.log(`  结果: ${smartResult.success ? '成功' : '失败'}`);
    console.log(`  耗时: ${time1}ms`);
    if (smartResult.success) {
      console.log(`  余额: ${smartResult.balance} SOL`);
      console.log(`  RPC 来源: ${smartResult.rpcSource || '未知'}`);
    } else {
      console.log(`  错误: ${smartResult.message}`);
    }
    
    // 测试强制使用 Helius
    console.log('\n2️⃣ 强制使用 Helius RPC:');
    const start2 = Date.now();
    const heliusConnection = await solanaRpcService.getConnection('getAccountInfo', true);
    const heliusBalance = await heliusConnection.getBalance(new (await import('@solana/web3.js')).PublicKey(testAddress));
    const time2 = Date.now() - start2;
    
    console.log(`  结果: 成功`);
    console.log(`  耗时: ${time2}ms`);
    console.log(`  余额: ${heliusBalance / **********} SOL`);
    console.log(`  RPC 来源: helius`);
    
    // 对比结果
    if (smartResult.success) {
      const balanceDiff = Math.abs(smartResult.balance - (heliusBalance / **********));
      console.log(`\n📊 结果对比:`);
      console.log(`  余额差异: ${balanceDiff} SOL`);
      console.log(`  数据一致性: ${balanceDiff < 0.000001 ? '✅ 一致' : '❌ 不一致'}`);
      console.log(`  性能对比: 智能选择 ${time1 < time2 ? '更快' : '更慢'} ${Math.abs(time1 - time2)}ms`);
    }
    
  } catch (error) {
    console.error('❌ 余额对比测试失败:', error);
  }
}

/**
 * 测试 CFX 代币余额获取
 */
async function testCfxTokenBalance() {
  console.log('\n🪙 === 测试 CFX 代币余额获取 ===');
  
  for (const address of TEST_WALLET_ADDRESSES) {
    console.log(`\n测试地址: ${address.substring(0, 8)}...${address.substring(address.length - 4)}`);
    
    try {
      const start = Date.now();
      const result = await solanaRpcService.getCfxTokenBalance(address);
      const time = Date.now() - start;
      
      console.log(`  结果: ${result.success ? '成功' : '失败'}`);
      console.log(`  耗时: ${time}ms`);
      
      if (result.success) {
        console.log(`  CFX 余额: ${result.balance} CFX`);
        console.log(`  RPC 来源: ${result.rpcSource || '未知'}`);
        if (result.message) {
          console.log(`  说明: ${result.message}`);
        }
      } else {
        console.log(`  错误: ${result.message}`);
      }
      
      // 添加延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 500));
      
    } catch (error) {
      console.error(`  ❌ 获取失败:`, error.message);
    }
  }
}

/**
 * 测试 RPC 方法分类
 */
async function testRpcMethodCategories() {
  console.log('\n📋 === 测试 RPC 方法分类 ===');
  
  const testMethods = [
    { method: 'getBalance', category: 'WALLET_RPC' },
    { method: 'getTokenAccountsByOwner', category: 'WALLET_RPC' },
    { method: 'getAccountInfo', category: 'HELIUS_RPC' },
    { method: 'getSlot', category: 'HELIUS_RPC' }
  ];
  
  for (const { method, category } of testMethods) {
    try {
      const connection = await solanaRpcService.getConnection(method);
      const status = solanaRpcService.getConnectionStatus();
      
      let actualCategory;
      if (connection.rpcEndpoint?.includes('helius')) {
        actualCategory = 'HELIUS_RPC';
      } else if (connection.rpcEndpoint?.includes('solana.com') || connection.rpcEndpoint?.includes('127.0.0.1')) {
        actualCategory = 'WALLET_RPC';
      } else {
        actualCategory = 'UNKNOWN';
      }
      
      console.log(`  ${method}:`);
      console.log(`    预期分类: ${category}`);
      console.log(`    实际分类: ${actualCategory}`);
      console.log(`    端点: ${connection.rpcEndpoint}`);
      console.log(`    分类正确: ${category === actualCategory ? '✅' : '❌'}`);
      
    } catch (error) {
      console.error(`  ❌ ${method} 测试失败:`, error.message);
    }
  }
}

/**
 * 测试错误处理和降级机制
 */
async function testErrorHandlingAndFallback() {
  console.log('\n🛡️ === 测试错误处理和降级机制 ===');
  
  // 测试无效地址
  console.log('\n1️⃣ 测试无效地址:');
  try {
    const result = await solanaRpcService.getBalance('invalid-address');
    console.log(`  结果: ${result.success ? '意外成功' : '正确失败'}`);
    console.log(`  错误信息: ${result.message}`);
  } catch (error) {
    console.log(`  捕获异常: ${error.message}`);
  }
  
  // 测试空地址
  console.log('\n2️⃣ 测试空地址:');
  try {
    const result = await solanaRpcService.getBalance('');
    console.log(`  结果: ${result.success ? '意外成功' : '正确失败'}`);
    console.log(`  错误信息: ${result.message}`);
  } catch (error) {
    console.log(`  捕获异常: ${error.message}`);
  }
  
  // 测试 CFX 代币查询错误处理
  console.log('\n3️⃣ 测试 CFX 代币查询错误处理:');
  try {
    const result = await solanaRpcService.getCfxTokenBalance('invalid-address');
    console.log(`  结果: ${result.success ? '意外成功' : '正确失败'}`);
    console.log(`  错误信息: ${result.message}`);
  } catch (error) {
    console.log(`  捕获异常: ${error.message}`);
  }
}

/**
 * 显示性能统计
 */
function showPerformanceStats() {
  console.log('\n📊 === 性能统计 ===');
  
  const status = solanaRpcService.getConnectionStatus();
  console.log('连接状态:');
  console.log(`  Helius RPC: ${status.heliusReady ? '可用' : '不可用'}`);
  console.log(`  钱包 RPC: ${status.walletReady ? '可用' : '不可用'}`);
  
  console.log('\n优化效果:');
  if (status.walletReady) {
    console.log('  ✅ 个人数据使用钱包 RPC，减少 Helius API 压力');
    console.log('  ✅ 公共数据使用 Helius RPC，保证数据可靠性');
    console.log('  ✅ 自动降级机制，确保服务可用性');
  } else {
    console.log('  ⚠️ 钱包 RPC 不可用，所有请求使用 Helius RPC');
    console.log('  💡 建议：连接钱包以启用混合 RPC 优化');
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始混合 RPC 测试');
  console.log('='.repeat(50));
  
  try {
    // 测试连接状态
    await testConnectionStatus();
    
    // 测试余额获取对比
    await testBalanceComparison();
    
    // 测试 CFX 代币余额
    await testCfxTokenBalance();
    
    // 测试 RPC 方法分类
    await testRpcMethodCategories();
    
    // 测试错误处理
    await testErrorHandlingAndFallback();
    
    // 显示性能统计
    showPerformanceStats();
    
    console.log('\n✅ 所有测试完成');
    
  } catch (error) {
    console.error('\n❌ 测试过程中出现错误:', error);
  }
}

// 运行测试
runTests().catch(console.error);
