/**
 * RPC 优化测试脚本
 * 测试 RPC 管理器的缓存和去重功能
 */

import rpcManager from '../src/services/rpcManager.js';
import walletDataLoader from '../src/services/walletDataLoader.js';

// 测试钱包地址
const TEST_WALLET_ADDRESSES = [
  'HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH', // 示例地址1
  '9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM', // 示例地址2
  'DhzDoryP2a4rMK2bcWwJxrE2uW6ir81ES8ZwJJLcSKrt'  // 示例地址3
];

/**
 * 测试 RPC 缓存功能
 */
async function testRPCCaching() {
  console.log('\n🧪 === 测试 RPC 缓存功能 ===');
  
  const testAddress = TEST_WALLET_ADDRESSES[0];
  console.log(`测试地址: ${testAddress}`);
  
  // 第一次调用（应该发起 RPC 请求）
  console.log('\n1️⃣ 第一次获取余额（应该发起 RPC 请求）');
  const start1 = Date.now();
  const result1 = await rpcManager.getBalance(testAddress);
  const time1 = Date.now() - start1;
  console.log(`结果: ${result1.success ? '成功' : '失败'}, 耗时: ${time1}ms`);
  if (result1.success) {
    console.log(`余额: ${result1.balance} SOL`);
  }
  
  // 第二次调用（应该使用缓存）
  console.log('\n2️⃣ 第二次获取余额（应该使用缓存）');
  const start2 = Date.now();
  const result2 = await rpcManager.getBalance(testAddress);
  const time2 = Date.now() - start2;
  console.log(`结果: ${result2.success ? '成功' : '失败'}, 耗时: ${time2}ms`);
  if (result2.success) {
    console.log(`余额: ${result2.balance} SOL`);
  }
  
  console.log(`\n⚡ 缓存效果: 第二次调用比第一次快 ${time1 - time2}ms`);
  
  // 强制刷新（应该发起新的 RPC 请求）
  console.log('\n3️⃣ 强制刷新（应该发起新的 RPC 请求）');
  const start3 = Date.now();
  const result3 = await rpcManager.getBalance(testAddress, true);
  const time3 = Date.now() - start3;
  console.log(`结果: ${result3.success ? '成功' : '失败'}, 耗时: ${time3}ms`);
  if (result3.success) {
    console.log(`余额: ${result3.balance} SOL`);
  }
}

/**
 * 测试请求去重功能
 */
async function testRequestDeduplication() {
  console.log('\n🔄 === 测试请求去重功能 ===');
  
  const testAddress = TEST_WALLET_ADDRESSES[0];
  console.log(`测试地址: ${testAddress}`);
  
  // 清除缓存确保测试准确性
  rpcManager.clearAddressCache(testAddress);
  
  console.log('\n同时发起 5 个相同的余额请求...');
  const start = Date.now();
  
  // 同时发起多个相同的请求
  const promises = Array(5).fill().map((_, index) => {
    console.log(`发起请求 ${index + 1}`);
    return rpcManager.getBalance(testAddress);
  });
  
  const results = await Promise.all(promises);
  const totalTime = Date.now() - start;
  
  console.log(`\n所有请求完成，总耗时: ${totalTime}ms`);
  console.log('结果统计:');
  results.forEach((result, index) => {
    console.log(`  请求 ${index + 1}: ${result.success ? '成功' : '失败'}`);
    if (result.success) {
      console.log(`    余额: ${result.balance} SOL`);
    }
  });
  
  // 检查所有结果是否一致
  const allSuccess = results.every(r => r.success);
  const allSameBalance = results.every(r => r.balance === results[0].balance);
  
  console.log(`\n✅ 去重效果: ${allSuccess && allSameBalance ? '成功' : '失败'}`);
  console.log(`   所有请求成功: ${allSuccess}`);
  console.log(`   余额一致: ${allSameBalance}`);
}

/**
 * 测试批量获取功能
 */
async function testBatchLoading() {
  console.log('\n📦 === 测试批量获取功能 ===');
  
  console.log(`测试地址数量: ${TEST_WALLET_ADDRESSES.length}`);
  TEST_WALLET_ADDRESSES.forEach((addr, index) => {
    console.log(`  ${index + 1}. ${addr}`);
  });
  
  console.log('\n开始批量获取余额...');
  const start = Date.now();
  
  const results = await rpcManager.getBatchBalances(TEST_WALLET_ADDRESSES);
  
  const totalTime = Date.now() - start;
  console.log(`批量获取完成，总耗时: ${totalTime}ms`);
  
  console.log('\n结果:');
  Object.entries(results).forEach(([address, result]) => {
    const shortAddr = `${address.substring(0, 4)}...${address.substring(address.length - 4)}`;
    console.log(`  ${shortAddr}: ${result.success ? `${result.balance} SOL` : '失败'}`);
  });
}

/**
 * 测试钱包数据加载器
 */
async function testWalletDataLoader() {
  console.log('\n🎯 === 测试钱包数据加载器 ===');
  
  const testAddress = TEST_WALLET_ADDRESSES[0];
  console.log(`测试地址: ${testAddress}`);
  
  console.log('\n获取完整钱包数据（SOL + CFX 余额）...');
  const start = Date.now();
  
  const walletData = await walletDataLoader.getWalletData(testAddress);
  
  const totalTime = Date.now() - start;
  console.log(`获取完成，总耗时: ${totalTime}ms`);
  
  console.log('\n结果:');
  console.log(`  SOL 余额: ${walletData.solBalance.success ? `${walletData.solBalance.balance} SOL` : '失败'}`);
  console.log(`  CFX 余额: ${walletData.cfxBalance.success ? `${walletData.cfxBalance.balance} CFX` : '失败'}`);
  
  // 测试智能刷新
  console.log('\n测试智能刷新策略...');
  const contexts = ['connect', 'stake', 'manual', 'background'];
  
  for (const context of contexts) {
    console.log(`\n  ${context} 上下文:`);
    const start = Date.now();
    const result = await walletDataLoader.smartRefresh(testAddress, context);
    const time = Date.now() - start;
    console.log(`    耗时: ${time}ms`);
    console.log(`    SOL: ${result.solBalance.success ? 'OK' : 'FAIL'}`);
    console.log(`    CFX: ${result.cfxBalance.success ? 'OK' : 'FAIL'}`);
  }
}

/**
 * 显示缓存统计信息
 */
function showCacheStats() {
  console.log('\n📊 === 缓存统计信息 ===');
  
  const rpcStats = rpcManager.getCacheStats();
  console.log('RPC 管理器:');
  console.log(`  缓存大小: ${rpcStats.cacheSize}`);
  console.log(`  进行中请求: ${rpcStats.pendingRequests}`);
  
  const loaderStats = walletDataLoader.getStats();
  console.log('\n钱包数据加载器:');
  console.log(`  缓存大小: ${loaderStats.cacheSize}`);
  console.log(`  进行中请求: ${loaderStats.pendingRequests}`);
  console.log(`  加载状态: ${loaderStats.loadingStates}`);
  console.log(`  队列长度: ${loaderStats.queueLength}`);
  console.log(`  正在处理队列: ${loaderStats.processingQueue}`);
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始 RPC 优化测试');
  console.log('='.repeat(50));
  
  try {
    // 显示初始缓存状态
    showCacheStats();
    
    // 运行各项测试
    await testRPCCaching();
    await testRequestDeduplication();
    await testBatchLoading();
    await testWalletDataLoader();
    
    // 显示最终缓存状态
    showCacheStats();
    
    console.log('\n✅ 所有测试完成');
    
  } catch (error) {
    console.error('\n❌ 测试过程中出现错误:', error);
  }
}

// 运行测试
runTests().catch(console.error);
